import React, { useCallback } from 'react';
import { View, FlatList, TouchableOpacity } from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { captureException } from 'src/services/datadog';
import { Loader } from 'src/components/primitives/Loader';
import { ErrorScreen as ErrorBlock } from 'src/screens/ErrorScreen';
import {
  INVOICE_CAMPAIGN_DETAILS_SCREEN,
  INVOICE_DETAILS_SCREEN,
  IS_WEB,
  VIEW_LEADS_SCREEN,
} from 'src/constants';
import type { AllScreensParamList } from 'src/navigation/routes';
import { useCampaigns } from 'src/screens/Campaigns/hooks/useCampaigns';
import { CampaignTypeEnum } from 'src/data/schemas/api/campaigns';
import {
  CAMPAIGN_INVOICE_DETAILS,
  CAMPAIGNS_LIST_ERROR,
  ZUORA_ITEMS_ERROR,
} from 'src/screens/Campaigns/constants';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { CategoryThumbnailWithSkeleton } from 'src/screens/Campaigns/components';
import {
  Button,
  Icon,
  Typography,
} from '@cat-home-experts/react-native-components';
import { formatPrice } from 'src/screens/Campaigns/utilities/formatPrice';
import { useZuoraInvoiceItems } from 'src/screens/MyBilling/components/Invoices/useZuoraInvoices/useZuoraInvoiceItems';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';

type CampaignWithDates = {
  campaignId: string;
  campaignType: CampaignTypeEnum;
  categoryId: number;
  categoryName: string;
  dateGeneratedTo?: string;
  dateGeneratedFrom?: string;
};

const TEST_IDS = createTestIds('campaign-invoice-details', {
  TITLE: 'title',
  LEAD_LINKS: 'lead-links',
  CAMPAIGN_HEADER: 'campaign-header',
  INVOICE_TOTAL: 'invoice-total',
  INVOICE_NUMBER: 'invoice-number',
  INVOICE_DUE_DATE: 'invoice-due-date',
  PDF_BUTTON: 'pdf-button',
});

export const CampaignInvoiceDetails: React.NativeFC<
  object,
  typeof TEST_IDS
> = () => {
  // params
  const route =
    useRoute<
      RouteProp<AllScreensParamList, typeof INVOICE_CAMPAIGN_DETAILS_SCREEN>
    >();
  const { invoiceTotal, invoiceNumber, invoiceDueDate, invoiceId } =
    route.params ?? {};
  if (!invoiceId) {
    throw new InvalidNavParamsError();
  }

  // hooks
  const navigation = useNavigation();
  const isDesktop = useDesktopMediaQuery();
  const {
    invoicesData: invoiceItems,
    isLoading: zuoraIsLoading,
    error: zuoraError,
  } = useZuoraInvoiceItems(invoiceId);
  const {
    campaigns,
    isLoading: campaignsIsLoading,
    error: fetchCampaignsError,
  } = useCampaigns({});

  // computed values
  const filteredItems = invoiceItems.filter((item) => item.campaignId);
  const campaignIdMap = new Map<string, { dateTo: string; dateFrom: string }>();
  filteredItems.forEach((item) =>
    campaignIdMap.set(item.campaignId!, {
      dateFrom: item.servicesStartDate,
      dateTo: item.servicesEndDate,
    }),
  );
  const filteredCampaigns = campaigns
    .filter((campaign) => campaignIdMap.has(campaign.campaignId))
    .map((campaign): CampaignWithDates => {
      const dates = campaignIdMap.get(campaign.campaignId);
      return {
        campaignId: campaign.campaignId,
        campaignType: campaign.campaignType,
        categoryId: campaign.category.categoryId,
        categoryName: campaign.category.name,
        dateGeneratedFrom: dates?.dateFrom,
        dateGeneratedTo: dates?.dateTo,
      };
    });

  // handlers
  const handleViewLead = useCallback(
    (campaign: CampaignWithDates) => {
      navigation.navigate(VIEW_LEADS_SCREEN, {
        campaignId: campaign.campaignId,
        dateGeneratedTo: campaign.dateGeneratedTo,
        dateGeneratedFrom: campaign.dateGeneratedFrom,
      });
    },
    [navigation],
  );
  const handleViewPdf = useCallback(() => {
    navigation.navigate(INVOICE_DETAILS_SCREEN, {
      invoiceId: invoiceId,
    });
  }, [invoiceId, navigation]);
  const renderItem = useCallback(
    ({ item }: { item: CampaignWithDates }) => (
      <View
        style={styles.campaignDetailsContainer}
        testID={`${TEST_IDS.ROOT}-${item.campaignId}`}
      >
        <CategoryThumbnailWithSkeleton categoryId={item.categoryId} />
        <View style={styles.textContainer}>
          <Typography useVariant="bodySemiBold" style={styles.blackText}>
            {item.categoryName}
          </Typography>
          <TouchableOpacity
            testID={TEST_IDS.LEAD_LINKS}
            style={styles.leadsLinkContainer}
            onPress={() => handleViewLead(item)}
          >
            <Typography useVariant="labelRegular">
              {CAMPAIGN_INVOICE_DETAILS.VIEW_LEADS}
            </Typography>
            <Icon style={styles.icon} name="chevron-right" size={16} />
          </TouchableOpacity>
        </View>
      </View>
    ),
    [handleViewLead],
  );

  // Errors
  if (campaignsIsLoading || zuoraIsLoading) {
    return <Loader />;
  }

  if (fetchCampaignsError) {
    captureException(fetchCampaignsError, {
      Component: 'CampaignInvoiceDetails',
    });
    return (
      <ErrorBlock
        title={CAMPAIGNS_LIST_ERROR.TITLE}
        message={CAMPAIGNS_LIST_ERROR.MESSAGE}
        style={styles.errorBlock}
      />
    );
  }

  if (zuoraError) {
    captureException(zuoraError, { Component: 'CampaignInvoiceDetails' });
    return (
      <ErrorBlock
        title={ZUORA_ITEMS_ERROR.TITLE}
        message={ZUORA_ITEMS_ERROR.MESSAGE}
        style={styles.errorBlock}
      />
    );
  }

  // render
  return (
    <View
      style={[styles.container, IS_WEB && isDesktop && styles.containerWeb]}
    >
      {campaignIdMap.size > 0 && (
        <>
          <Typography
            style={styles.campaignsHeader}
            use="subHeader"
            testID={TEST_IDS.CAMPAIGN_HEADER}
            isBranded
          >
            {CAMPAIGN_INVOICE_DETAILS.HEADER}
          </Typography>
          <FlatList
            data={filteredCampaigns}
            renderItem={renderItem}
            contentContainerStyle={styles.flatListContainer}
            style={styles.flatList}
          />
        </>
      )}
      <View style={styles.invoiceContent}>
        <View style={styles.invoiceText}>
          <Typography useVariant="bodyBold">
            {CAMPAIGN_INVOICE_DETAILS.TOTAL}
          </Typography>
          <Typography useVariant="bodyBold" testID={TEST_IDS.INVOICE_TOTAL}>
            {formatPrice(invoiceTotal ?? 0)}
          </Typography>
        </View>
        <View style={styles.divider} />
        <View style={styles.invoiceText}>
          <Typography useVariant="bodySemiBold">
            {CAMPAIGN_INVOICE_DETAILS.INVOICE_NUMBER}
          </Typography>
          <Typography
            useVariant="bodySemiBold"
            testID={TEST_IDS.INVOICE_NUMBER}
          >
            {invoiceNumber}
          </Typography>
        </View>
        <View style={styles.divider} />
        <View style={styles.invoiceText}>
          <Typography useVariant="bodySemiBold">
            {CAMPAIGN_INVOICE_DETAILS.DUE_DATE}
          </Typography>
          <Typography
            useVariant="bodySemiBold"
            testID={TEST_IDS.INVOICE_DUE_DATE}
          >
            {invoiceDueDate}
          </Typography>
        </View>
        <View style={styles.divider} />
        <Button
          label="View invoice as PDF"
          block
          variant="secondary"
          onPress={handleViewPdf}
          testID={TEST_IDS.PDF_BUTTON}
        />
      </View>
    </View>
  );
};

CampaignInvoiceDetails.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  container: {
    flex: 1,
    width: '100%',
    backgroundColor: palette.mortar.tokenColorLightBlue,
    padding: spacing(1),
    marginHorizontal: 'auto',
  },
  containerWeb: {
    maxWidth: spacing(102),
    paddingHorizontal: spacing(1),
    paddingVertical: spacing(3),
  },
  flatList: {
    height: '25%',
    marginBottom: 0,
    flexShrink: 0,
    flexGrow: 0,
  },
  flatListContainer: {
    width: '100%',
    alignSelf: 'center',
    padding: spacing(2),
    gap: spacing(1),
  },
  campaignDetailsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    padding: spacing(2),
    borderColor: palette.mortarV3.tokenNeutral300,
    borderWidth: 1,
    borderRadius: spacing(0.5),
  },
  textContainer: {
    flexDirection: 'row',
    width: '100%',
    flex: 1,
  },
  blackText: { color: palette.mortar.tokenColorBlack },
  leadsLinkContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    flex: 1,
  },
  errorBlock: {
    flex: 1,
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  icon: { marginLeft: 2 },
  campaignsHeader: {
    margin: spacing(2),
    marginBottom: 0,
  },
  invoiceContent: {
    backgroundColor: palette.mortar.tokenColorLightBlue,
    padding: spacing(3),
    gap: spacing(2),
    maxWidth: 800,
  },
  divider: {
    height: 1,
    width: '92%',
    backgroundColor: palette.mortar.tokenColorLighterGrey,
    alignSelf: 'center',
  },
  invoiceText: {
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
}));

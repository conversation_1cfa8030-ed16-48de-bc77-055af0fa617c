import React, { useState, useEffect, ReactElement, useCallback } from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';

import { RouteProp, useRoute } from '@react-navigation/native';

import {
  tokenColorPrimaryBlue,
  tokenColorPrimaryWhite,
} from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';

import { useUserContext } from 'src/hooks/useUser';
import { captureException } from 'src/services/datadog';

import { downloadInvoice } from 'src/services/zuoraUtils';
import { Loader } from 'src/components/primitives/Loader';
import { ErrorScreen as ErrorBlock } from 'src/screens/ErrorScreen';
import { INVOICE_DETAILS_SCREEN } from 'src/constants';
import type { AllScreensParamList } from 'src/navigation/routes';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.mjs',
  import.meta.url,
).toString();

export function InvoiceDetails(): ReactElement {
  const route =
    useRoute<RouteProp<AllScreensParamList, typeof INVOICE_DETAILS_SCREEN>>();
  const { companyId } = useUserContext();
  const [fileUrl, setFileUrl] = useState('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [hasDownloadError, setHasDownloadError] = useState(false);
  const { invoiceId } = route.params ?? {};
  if (!invoiceId) {
    throw new InvalidNavParamsError();
  }

  const handleInvoiceRetrieval = useCallback(
    (companyIdForInvoice: number) => {
      setIsLoading(true);
      downloadInvoice(companyIdForInvoice, invoiceId)
        .then((data) => {
          setFileUrl(data);
          setIsLoading(false);
        })
        .catch((error) => {
          captureException(
            new Error(
              `Unable to download invoice file, Error: ${
                (error as Error)?.message
              }`,
            ),
          );
          setHasDownloadError(true);
          setIsLoading(false);
        });
    },
    [invoiceId],
  );

  useEffect(() => {
    if (companyId) {
      handleInvoiceRetrieval(companyId);
    }
  }, [companyId, handleInvoiceRetrieval]);

  if (hasDownloadError) {
    return (
      <View style={styles.errorMessage}>
        <ErrorBlock
          title="Sorry the PDF file failed to load"
          message="Please try again later"
        />
      </View>
    );
  }

  if (isLoading) {
    return <Loader />;
  }

  return (
    <View style={styles.container}>
      {fileUrl ? (
        <>
          <ScrollView style={styles.scrollView}>
            <Document file={{ url: fileUrl }}>
              <Page pageNumber={1} />
            </Document>
          </ScrollView>
          <a
            // TODO: upgrade-eslint - fix color literals
            // eslint-disable-next-line react-native/no-color-literals, react-native/no-inline-styles
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              margin: 'auto',
              padding: '8px 16px',
              position: 'absolute',
              height: 40,
              left: 15,
              right: 14,
              bottom: 16,
              background: tokenColorPrimaryBlue,
              borderRadius: 4,
              color: 'white',
              textDecoration: 'none',
              fontFamily: 'semi-bold',
            }}
            href={fileUrl}
            download={`${invoiceId}.pdf`}
          >
            {'Download as PDF'}
          </a>
        </>
      ) : null}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: tokenColorPrimaryWhite,
  },
  scrollView: {
    flex: 1,
  },
  errorMessage: {
    margin: 'auto',
  },
});

import React, { useState, useEffect, useCallback, FC } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import PDFReader from 'rn-pdf-reader-js';

import { RouteProp, useRoute } from '@react-navigation/native';

import * as Sharing from 'expo-sharing';

import { Button } from '@cat-home-experts/react-native-components';
import { tokenColorPrimaryWhite } from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';

import { useUserContext } from 'src/hooks/useUser';

import { captureException } from 'src/services/sentry';
import { downloadInvoice } from 'src/services/zuoraUtils';
import { Loader } from 'src/components/primitives/Loader';
import { ErrorScreen as ErrorBlock } from 'src/screens/ErrorScreen';
import { INVOICE_DETAILS_SCREEN } from 'src/constants';
import type { AllScreensParamList } from 'src/navigation/routes';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';

export const InvoiceDetails = (): ReturnType<FC> => {
  const route =
    useRoute<RouteProp<AllScreensParamList, typeof INVOICE_DETAILS_SCREEN>>();
  const { companyId } = useUserContext();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [localFilePath, setLocalFilePath] = useState<string>();
  const [hasDownloadError, setHasDownloadError] = useState(false);
  const [isSharingEnabled, setIsSharingEnabled] = useState<boolean>(false);
  const { invoiceId } = route.params ?? {};
  if (!invoiceId) {
    throw new InvalidNavParamsError();
  }

  const handleInvoiceRetrieval = useCallback(
    (companyIdForInvoice: number) => {
      setIsLoading(true);
      downloadInvoice(companyIdForInvoice, invoiceId)
        .then((filePath) => {
          setIsLoading(false);
          setLocalFilePath(filePath);
        })
        .catch((error) => {
          setIsLoading(false);
          setHasDownloadError(true);
          captureException(
            new Error(
              `Unable to download invoice file, Error: ${
                (error as Error)?.message
              }`,
            ),
            { extra: { invoiceId } },
          );
        });
    },
    [invoiceId],
  );

  const onPressShareInvoice = useCallback(async () => {
    if (localFilePath) {
      await Sharing.shareAsync(localFilePath, { UTI: 'pdf' });
    }
  }, [localFilePath]);

  useEffect(() => {
    if (companyId) {
      handleInvoiceRetrieval(companyId);
    }
  }, [companyId, handleInvoiceRetrieval]);

  useEffect(() => {
    Sharing.isAvailableAsync().then((isEnabled) =>
      setIsSharingEnabled(isEnabled),
    );
  }, []);

  if (hasDownloadError) {
    return (
      <ErrorBlock
        title="Sorry the PDF file failed to load"
        message="Please try again later"
        style={styles.container}
      />
    );
  }

  if (isLoading) {
    return <Loader />;
  }

  return (
    <ScrollView contentContainerStyle={styles.container}>
      {localFilePath ? (
        <>
          <PDFReader source={{ uri: localFilePath }} />
          {isSharingEnabled ? (
            <View style={styles.ctaContainer}>
              <Button
                variant="secondary"
                label="Share invoice"
                block
                onPress={onPressShareInvoice}
              />
            </View>
          ) : null}
        </>
      ) : null}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: tokenColorPrimaryWhite,
  },
  ctaContainer: {
    position: 'absolute',
    alignSelf: 'center',
    bottom: '10%',
    width: '95%',
    maxWidth: 256,
  },
});

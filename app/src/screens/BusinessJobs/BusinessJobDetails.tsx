import {
  Button,
  Dropdown,
  Typography,
} from '@cat-home-experts/react-native-components';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { useRoute, type RouteProp } from '@react-navigation/native';
import React, { useState } from 'react';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import { Loader, showToast } from 'src/components';
import { BUSINESS_JOB_DETAILS_SCREEN, JOBS_SCREEN } from 'src/constants';
import type { AllScreensParamList } from 'src/navigation/routes';
import { PageNotFound } from 'src/screens/PageNotFound';
import { openExternalLink } from 'src/utilities/openExternalLink';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { ActionLayout } from './components/ActionLayout';
import { BusinessJobCardContent } from './components/BusinessJobCardContent';
import { DeclineJobModal } from './components/DeclineJobModal';
import { EarningsSection } from './components/EarningsSection';
import { SelectDateDropdownTrigger } from './components/SelectDateDropdownTrigger';
import { BUSINESS_JOB_DETAILS_COPY } from './constants';
import { useBusinessJobDetails } from './hooks/useBusinessJobDetails';
import { formatDateOption, priceToMinorUnits } from './utils';

type CurrentRoute = RouteProp<
  AllScreensParamList,
  typeof BUSINESS_JOB_DETAILS_SCREEN
>;

export function BusinessJobDetails(): ReturnType<React.FC> {
  const route = useRoute<CurrentRoute>();
  const { id } = route.params ?? {};
  if (!id) {
    throw new InvalidNavParamsError();
  }

  const {
    jobDetails,
    isLoading,
    acceptJob,
    rejectJob,
    cancelJob,
    completeJob,
    isJobStale,
    isJobRequested,
    isJobDeclined,
  } = useBusinessJobDetails(id);
  const [selectedDateId, setSelectedDateId] = useState<string>('');
  const [isDateDropdownVisible, setIsDateDropdownVisible] = useState(false);

  const [isDeclineModalVisible, setIsDeclineModalVisible] = useState(false);

  if (isLoading) {
    return <Loader />;
  }

  if (!jobDetails?.job) {
    return <PageNotFound fallbackScreenToNavigate={JOBS_SCREEN} />;
  }

  const job = jobDetails.job;

  if (!isJobStale) {
    return (
      <ScrollView style={styles.container}>
        <ActionLayout
          jobDetails={jobDetails}
          cancelJob={cancelJob}
          completeJob={completeJob}
        />
      </ScrollView>
    );
  }

  const getBanner = () => {
    if (!isJobRequested || isJobDeclined) {
      return null;
    }

    return (
      <View style={styles.header}>
        <Typography useVariant="headingSMSemiBold" style={styles.expiringText}>
          {BUSINESS_JOB_DETAILS_COPY.BANNER.TITLE}
        </Typography>
        <Typography useVariant="bodyRegular" style={styles.expiringSubtext}>
          {BUSINESS_JOB_DETAILS_COPY.BANNER.DESCRIPTION}
        </Typography>
      </View>
    );
  };

  const handleAcceptJob = async () => {
    try {
      await acceptJob({ selectedDateId });
      showToast({
        text1: BUSINESS_JOB_DETAILS_COPY.TOAST.JOB_ACCEPTED_SUCCESS,
        type: 'success',
      });
    } catch (error) {
      showToast({
        text1: BUSINESS_JOB_DETAILS_COPY.TOAST.JOB_ACCEPTED_ERROR,
        type: 'error',
      });
    }
  };

  const handleDeclineJob = async (reason: {
    reason: string;
    suggestedPrice?: string;
  }) => {
    try {
      await rejectJob({
        reason: reason.reason,
        suggestedPrice: priceToMinorUnits(reason.suggestedPrice),
      });
      showToast({
        text1: BUSINESS_JOB_DETAILS_COPY.TOAST.JOB_DECLINED_SUCCESS,
        type: 'success',
      });
      setIsDeclineModalVisible(false);
    } catch (error) {
      showToast({
        text1: BUSINESS_JOB_DETAILS_COPY.TOAST.JOB_DECLINED_ERROR,
        type: 'error',
      });
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        {getBanner()}
        <View style={styles.jobCardContainer}>
          <BusinessJobCardContent jobDetails={jobDetails} />
          {isJobRequested && (
            <View style={styles.footerContainer}>
              <View style={styles.footerContentWrapper}>
                <View>
                  <Typography
                    useVariant="bodySemiBold"
                    style={styles.dateLabel}
                  >
                    {BUSINESS_JOB_DETAILS_COPY.DATE_SELECTION.LABEL}
                  </Typography>
                  <Dropdown
                    options={job.dates || []}
                    value={
                      job.dates?.find((date) => date.id === selectedDateId) ||
                      null
                    }
                    keyGetter={(date) => date.id}
                    displayGetter={(date) => formatDateOption(date)}
                    onChange={(date) => setSelectedDateId(date.id)}
                    presented={isDateDropdownVisible}
                    onChangePresented={setIsDateDropdownVisible}
                    snapPoints={['100%']}
                    renderTrigger={() => (
                      <SelectDateDropdownTrigger
                        selectedDate={job.dates?.find(
                          (date) => date.id === selectedDateId,
                        )}
                        onPress={() => setIsDateDropdownVisible(true)}
                      />
                    )}
                  />
                </View>
                <EarningsSection
                  minorUnits={job.price}
                  jobStatus={job.status}
                />
              </View>
              <View style={styles.termsContainer}>
                <View style={styles.termsTextContainer}>
                  <Typography useVariant="bodyRegular">
                    {BUSINESS_JOB_DETAILS_COPY.TERMS.PREFIX}{' '}
                  </Typography>
                  <TouchableOpacity
                    onPress={() => {
                      openExternalLink(
                        BUSINESS_JOB_DETAILS_COPY.TERMS.LINK_URL,
                      );
                    }}
                  >
                    <Typography
                      useVariant="bodySemiBold"
                      style={styles.pageLink}
                    >
                      {BUSINESS_JOB_DETAILS_COPY.TERMS.LINK}
                    </Typography>
                  </TouchableOpacity>
                </View>
              </View>
              <View style={styles.ctaContainer}>
                <Button
                  variant="outline"
                  label={BUSINESS_JOB_DETAILS_COPY.BUTTONS.NOT_FOR_ME}
                  onPress={() => setIsDeclineModalVisible(true)}
                  style={styles.declineButton}
                />
                <Button
                  variant="secondary"
                  label={BUSINESS_JOB_DETAILS_COPY.BUTTONS.ACCEPT_JOB}
                  onPress={handleAcceptJob}
                  isDisabled={!selectedDateId}
                />
              </View>
            </View>
          )}
        </View>

        <DeclineJobModal
          isVisible={isDeclineModalVisible}
          onClose={() => setIsDeclineModalVisible(false)}
          onSubmit={handleDeclineJob}
        />
      </View>
    </ScrollView>
  );
}

const styles = createMortarStyles(({ spacing, palette }) => ({
  container: {
    flex: 1,
    backgroundColor: palette.mortarV3.tokenNeutral100,
  },
  content: {
    padding: spacing(2),
  },
  header: {
    backgroundColor: palette.mortarV3.tokenPrimary200,
    padding: spacing(3),
    marginBottom: spacing(2),
    borderRadius: spacing(1),
  },
  expiringText: {
    color: palette.mortarV3.tokenNeutral900,
    marginBottom: spacing(1),
  },
  expiringSubtext: {
    color: palette.mortarV3.tokenNeutral600,
  },
  jobCardContainer: {
    backgroundColor: palette.mortarV3.tokenNeutral0,
    borderRadius: spacing(1),
    borderWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral200,
  },
  footerContainer: {
    flexDirection: 'column',
  },
  footerContentWrapper: {
    backgroundColor: palette.mortarV3.tokenDefault100,
    padding: spacing(3),
    gap: spacing(2),
  },
  ctaContainer: {
    padding: spacing(3),
    flexDirection: 'row',
    gap: spacing(2),
  },
  dateLabel: {
    marginBottom: spacing(1),
  },
  declineButton: {
    flex: 1,
  },
  acceptButton: {
    paddingRight: spacing(5),
    paddingLeft: spacing(5),
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: spacing(2),
    borderRadius: spacing(0.5),
  },
  dropdownItemSelected: {
    backgroundColor: palette.mortarV3.tokenPrimary200,
  },
  dropdownItemText: {
    color: palette.mortarV3.tokenNeutral900,
  },
  dropdownItemTextSelected: {
    color: palette.mortarV3.tokenPrimary700,
  },
  termsContainer: {
    padding: spacing(3),
    paddingBottom: 0,
  },
  termsTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  pageLink: {
    color: palette.mortarV3.tokenDefault500,
  },
}));

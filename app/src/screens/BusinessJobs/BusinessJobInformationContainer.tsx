import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { RouteProp, useRoute } from '@react-navigation/native';
import React, { ReactElement } from 'react';
import { ScrollView, View } from 'react-native';
import { Loader } from 'src/components';
import { BUSINESS_JOB_INFORMATION_SCREEN } from 'src/constants';
import type { AllScreensParamList } from 'src/navigation/routes';
import { ErrorScreen } from 'src/screens/ErrorScreen';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { useBusinessJobDetails } from './hooks/useBusinessJobDetails';
import { BUSINESS_JOB_INFORMATION_COPY } from './constants';
import { EarningsSection } from './components/EarningsSection';
import { BusinessJobCardContent } from './components/BusinessJobCardContent';

export const BusinessJobInformationContainer = (): ReactElement => {
  const route =
    useRoute<
      RouteProp<AllScreensParamList, typeof BUSINESS_JOB_INFORMATION_SCREEN>
    >();
  const { id } = route.params ?? {};
  if (!id) {
    throw new InvalidNavParamsError();
  }

  const { jobDetails, isLoading } = useBusinessJobDetails(id);

  if (isLoading) {
    return <Loader />;
  }

  if (!jobDetails?.job) {
    return (
      <ErrorScreen title={BUSINESS_JOB_INFORMATION_COPY.ERROR.JOB_NOT_FOUND} />
    );
  }

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
    >
      <BusinessJobCardContent jobDetails={jobDetails} />
      <View style={styles.earningsSection}>
        <EarningsSection
          minorUnits={jobDetails.job.price}
          jobStatus={jobDetails.job.status}
        />
      </View>
    </ScrollView>
  );
};

const styles = createMortarStyles(({ spacing, palette }) => ({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingVertical: spacing(2),
    backgroundColor: palette.mortarV3.tokenNeutral0,
  },
  earningsSection: {
    paddingHorizontal: spacing(3),
    paddingBottom: spacing(3),
  },
}));

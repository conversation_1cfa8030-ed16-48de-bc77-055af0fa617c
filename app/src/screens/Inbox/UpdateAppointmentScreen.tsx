import React, { useCallback, useMemo } from 'react';
import { View } from 'react-native';
import { useAtom } from 'jotai';
import { ScrollView } from 'react-native-gesture-handler';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import {
  createMortarStyles,
  isTruthy,
} from '@cat-home-experts/react-native-utilities';
import {
  AppointmentTimesCreated,
  BasicAccordion,
  CalendarEventStatus,
  LoadingGuard,
  Typography,
} from '@cat-home-experts/react-native-components';
import { showToast } from 'src/components';
import { captureException } from 'src/services/datadog';
import { IS_WEB, UPDATE_APPOINTMENT_SCREEN } from 'src/constants';
import { PromptMessage } from 'src/components/PromptMessage';
import { useUserContext } from 'src/hooks/useUser';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { useMarketplaceJobDetails } from 'src/screens/MarketplaceJobs/hooks/useMarketplaceJobDetails';
import { ContentSegment } from 'src/components/ContentSegment';
import type { AllScreensParamList } from 'src/navigation/routes';
import {
  AppointmentForm,
  AppointmentFormProps,
} from './components/Appointments/AppointmentForm';
import { CancelAppointmentBottomSheet } from './components/Appointments/CancelAppointmentBottomSheet';
import {
  activeChannelAtom,
  cancelAppointmentIdAtom,
} from './state/channelState';
import { getChannelConsumerMember } from './utils';
import { useAppointment } from './hooks/useAppointment';
import { useSetChannel } from './hooks/useSetChannel';

export const UpdateAppointmentScreen: React.FC = () => {
  const { goBack } = useNavigation();
  const { companyId } = useUserContext();
  const [channel] = useAtom(activeChannelAtom);
  const [cancelAppointmentId, setCancelAppointmentId] = useAtom(
    cancelAppointmentIdAtom,
  );
  const { job } = useMarketplaceJobDetails(channel?.data?.correlationId);
  const route =
    useRoute<
      RouteProp<AllScreensParamList, typeof UPDATE_APPOINTMENT_SCREEN>
    >();
  const { appointmentId } = route.params ?? {};

  const { updateAppointment, rescheduleAppointment, appointment, isLoading } =
    useAppointment(route.params?.appointmentId);

  useSetChannel(route.params?.channelId);

  const enableActions = useMemo(() => {
    const actionableStatuses: CalendarEventStatus[] = [
      CalendarEventStatus.CREATED,
      CalendarEventStatus.ACCEPTED,
    ];
    return (
      isTruthy(appointment?.status) &&
      actionableStatuses.includes(appointment.status)
    );
  }, [appointment]);

  const isReschedule = useMemo(
    () =>
      isTruthy(appointment?.status) &&
      appointment.status === CalendarEventStatus.ACCEPTED,
    [appointment?.status],
  );

  // Appointment times can either be edited inline, or a new set of times can be suggested, depending on the status of the appointment
  const isInlineUpdate = useMemo(() => {
    const { rescheduleRequestedByTrade } = route.params ?? {};

    if (!rescheduleRequestedByTrade) {
      return false;
    }

    if (isReschedule) {
      return false;
    }

    const allAppointentsInPast = appointment?.alternatives.every(
      (alternative) => new Date(alternative.start) < new Date(),
    );
    if (allAppointentsInPast) {
      return false;
    }

    return true;
  }, [route.params, isReschedule, appointment]);

  const promptMessage = useMemo(() => {
    const {
      rescheduleRequestedByTrade,
      rescheduleRequestedByFirstName,
      rescheduleRequestedTimeString,
    } = route.params ?? {};

    if (isInlineUpdate) {
      return '';
    }

    if (rescheduleRequestedByTrade) {
      return "You're rescheduling the appointment";
    }

    if (isTruthy(rescheduleRequestedTimeString)) {
      return `${rescheduleRequestedByFirstName || 'The customer'} has requested to reschedule to ${rescheduleRequestedTimeString.toLowerCase()}`;
    }

    return `${rescheduleRequestedByFirstName || 'The customer'} has requested different dates and times`;
  }, [isInlineUpdate, route.params]);

  const handleSend: AppointmentFormProps['onSend'] = useCallback(
    async (appointmentDetails) => {
      try {
        if (!job?.id) {
          throw new Error('Missing job data');
        }

        if (!channel) {
          throw new Error('Missing channel data');
        }

        if (!companyId) {
          throw new Error('Missing company data');
        }

        if (!appointmentId) {
          throw new Error('Missing appointmentId');
        }

        // TODO: Get consumerId from new jobs service when available
        const consumer = await getChannelConsumerMember(channel, companyId);

        if (!consumer?.user_id) {
          throw new Error('Consumer details not found');
        }

        if (isReschedule) {
          await rescheduleAppointment({
            type: appointmentDetails.type,
            alternatives: appointmentDetails.alternatives,
            consumerId: consumer.user_id,
            jobId: job.id,
          });
        } else {
          await updateAppointment({
            type: appointmentDetails.type,
            alternatives: appointmentDetails.alternatives,
          });
        }

        logEvent(EVENT_TYPE.APPOINTMENT_UPDATED, {
          appointmentId,
        });

        showToast({
          text1: 'Appointment times have been updated',
          type: 'success',
        });

        goBack();
      } catch (e) {
        showToast({
          text1: 'Unable to update appointment times, please try again',
          type: 'error',
        });
        captureException(e, {
          tags: {
            module: 'UpdateAppointmentScreen',
            method: 'handleSend',
          },
        });
      }
    },
    [
      appointmentId,
      goBack,
      rescheduleAppointment,
      updateAppointment,
      isReschedule,
      job,
      channel,
      companyId,
    ],
  );

  const handleCancelAppointment = useCallback(() => {
    if (!isTruthy(appointment)) {
      return;
    }

    setCancelAppointmentId(appointment.id);
  }, [setCancelAppointmentId, appointment]);

  const formInitialDetails = useMemo(() => {
    if (!appointment) {
      return undefined;
    }

    if (isInlineUpdate) {
      return { ...appointment, title: appointment.type.title || '' };
    }

    return {
      ...appointment,
      title: appointment.type.title || '',
      alternatives: [],
    };
  }, [appointment, isInlineUpdate]);

  return (
    <>
      <LoadingGuard style={styles.loadingGuard}>
        {!isLoading && (
          <ScrollView
            style={styles.root}
            contentContainerStyle={styles.contentContainer}
          >
            {!isInlineUpdate && (
              <ContentSegment style={styles.rescheduleWrapper}>
                <PromptMessage title={promptMessage} type="info" />
                <View>
                  {IS_WEB ? (
                    <View style={styles.rescheduleContentWeb}>
                      <Typography useVariant="bodyBold">
                        {'You previously suggested these times'}
                      </Typography>
                      <AppointmentTimesCreated
                        dateRanges={appointment?.alternatives ?? []}
                      />
                    </View>
                  ) : (
                    <BasicAccordion
                      primaryItem="You previously suggested these times"
                      style={styles.accordion}
                    >
                      <View style={styles.rescheduleContent}>
                        <AppointmentTimesCreated
                          dateRanges={appointment?.alternatives ?? []}
                        />
                      </View>
                    </BasicAccordion>
                  )}
                </View>
              </ContentSegment>
            )}
            <AppointmentForm
              onSend={handleSend}
              onCancel={handleCancelAppointment}
              initialAppointmentDetails={formInitialDetails}
              disabled={!enableActions}
            />
          </ScrollView>
        )}
      </LoadingGuard>
      <CancelAppointmentBottomSheet
        appointmentId={cancelAppointmentId}
        onCancel={goBack}
      />
    </>
  );
};

const styles = createMortarStyles(({ palette, spacing }) => ({
  loadingGuard: {
    flex: 1,
  },
  root: {
    flexGrow: 1,
    flexShrink: IS_WEB ? 1 : 0,
    backgroundColor: IS_WEB
      ? palette.mortar.tokenColorLightBlue
      : palette.mortarV3.tokenNeutral100,
    paddingTop: IS_WEB ? spacing(1) : 0,
  },
  contentContainer: {
    width: '100%',
    maxWidth: spacing(100),
    marginHorizontal: 'auto',
  },
  rescheduleWrapper: {
    paddingTop: spacing(2), // To account for the top margin in PromptMessage
    marginBottom: spacing(1),
    gap: spacing(1.5),
  },
  rescheduleContentWeb: {
    gap: spacing(2),
  },
  rescheduleContent: {
    marginHorizontal: spacing(2),
  },
  accordion: {
    borderWidth: 0,
    // Adjust for inner padding on base component
    marginHorizontal: -spacing(2),
    marginVertical: -spacing(1),
    width: 'auto',
  },
  accordionContent: {
    paddingHorizontal: spacing(2),
    paddingVertical: spacing(1),
  },
}));

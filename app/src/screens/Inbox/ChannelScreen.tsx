import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { View } from 'react-native';
import {
  Channel,
  Chat,
  MessageInput,
  MessageList,
  OverlayProvider,
} from 'stream-chat-expo';
import { useAtom } from 'jotai';
import { useHeaderHeight } from '@react-navigation/elements';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import {
  createMortarStyles,
  isTruthy,
} from '@cat-home-experts/react-native-utilities';
import { messageActions } from 'src/context/StreamProvider/utilities';
import { StreamConnectionState, useStreamManager } from 'src/services/stream';

import {
  CHANNEL_APPOINTMENTS_SCREEN,
  CHANNEL_SCREEN,
  MARKETPLACE_JOB_DETAILS_SCREEN,
} from 'src/constants';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import {
  isJobAccepted,
  isJobD<PERSON>lined,
  isJob<PERSON>ffered,
} from 'src/screens/MarketplaceJobs/utilities';
import { useMarketplaceJobDetails } from 'src/screens/MarketplaceJobs/hooks/useMarketplaceJobDetails';
import { Spinner } from 'src/components/primitives/Spinner';
import { captureException } from 'src/services/datadog';
import { showToast } from 'src/components';
import { useTrackChatIssues } from 'src/screens/Inbox/hooks/useTrackChatIssues';
import { useUserContext } from 'src/hooks/useUser';
import { useQuickActions } from 'src/hooks/useQuickActions/useQuickActions';
import { ChatGenerics } from 'src/types';
import { QuickActionsMessage } from 'src/components/QuickActions/QuickActionsMessage';
import { QuickActionsChatInput } from 'src/components/QuickActions/QuickActionsChatInput';
import { tradeAppBff } from 'src/data/api/trade-app-bff';
import type { AllScreensParamList } from 'src/navigation/routes';
import {
  quoteMessageAtom,
  quoteOptionsBottomSheetVisibleAtom,
  activeChannelAtom,
  appointmentOptionsAtom,
  cancelAppointmentIdAtom,
  channelOptionsBottomSheetVisibleAtom,
  invoiceOptionsBottomSheetVisibleAtom,
  invoiceMessageAtom,
} from './state/channelState';
import { ChannelJobDetailsHeader } from './components/ChannelJobDetailsHeader';
import { ChatInput, ChatInputRef } from './components/ChatInput';
import { InterestedInJobCTA } from './components/InterestedInJobCTA';
import { ChannelOptionsBottomSheet } from './components/ChannelOptionsBottomSheet';
import { ChatGuide } from './components/ChatGuide';
import { JobPaymentsGuide } from '../JobPayments/components/JobPaymentsGuide';
import { SmartMessage, SmartMessageProps } from './components/SmartMessages';
import { AppointmentOptionsBottomSheet } from './components/Appointments/AppointmentOptionsBottomSheet';
import { CancelAppointmentBottomSheet } from './components/Appointments/CancelAppointmentBottomSheet';
import { useChatGuide } from './hooks/useChatGuide';
import { useChatJobPaymentsGuide } from './hooks/useChatJobPaymentsGuide';
import { myMessageTheme, theme } from './theme';
import { ChatInputProps } from './types';
import { QuoteBottomSheet } from './components/Quotes/QuoteBottomSheet';
import { InvoiceBottomSheet } from './components/Quotes/InvoiceBottomSheet';
import { getConsumerIdFromChannel } from './utils';
import { useSetChannel } from './hooks/useSetChannel';
import { ChannelFrozen } from './components/ChannelFrozen';
import { ReportCustomerConfirmationBottomSheet } from './components/ReportCustomerConfirmationBottomSheet';

export const ChannelScreen: React.FC = () => {
  // Global State
  const [channel] = useAtom(activeChannelAtom);
  const [appointmentOptions, setAppointmentOptions] = useAtom(
    appointmentOptionsAtom,
  );
  const [cancelAppointmentId] = useAtom(cancelAppointmentIdAtom);
  const [
    channelOptionsBottomSheetVisible,
    setChannelOptionsBottomSheetVisible,
  ] = useAtom(channelOptionsBottomSheetVisibleAtom);
  const [quoteMessage, setQuoteMessage] = useAtom(quoteMessageAtom);
  const [quoteOptionsBottomSheetVisible, setQuoteOptionsBottomSheetVisible] =
    useAtom(quoteOptionsBottomSheetVisibleAtom);
  const [invoiceMessage, setInvoiceMessage] = useAtom(invoiceMessageAtom);
  const [
    invoiceOptionsBottomSheetVisible,
    setInvoiceOptionsBottomSheetVisible,
  ] = useAtom(invoiceOptionsBottomSheetVisibleAtom);
  const [
    reportCustomerConfirmationBottomSheetVisible,
    setReportCustomerConfirmationBottomSheetVisible,
  ] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Computed Values
  const { bottom } = useSafeAreaInsets();
  const headerHeight = useHeaderHeight();
  const { companyId } = useUserContext();
  const navigation = useNavigation();
  const route =
    useRoute<RouteProp<AllScreensParamList, typeof CHANNEL_SCREEN>>();
  const { channelId } = route.params ?? {};
  const { client, state } = useStreamManager();
  const { job, jobError } = useMarketplaceJobDetails(
    channel?.data?.correlationId,
  );
  const jobPaymentsGuideProps = useChatJobPaymentsGuide();

  const chatGuideProps = useChatGuide();
  const chatInputRef = useRef<ChatInputRef>(null);

  const quickActions = useQuickActions(channel, job, chatInputRef);
  const { hasRespondedToCustomer, ...quickActionHandlers } = quickActions;
  // Methods
  const handleBlockCustomer = useCallback(async () => {
    setChannelOptionsBottomSheetVisible(false);
    setReportCustomerConfirmationBottomSheetVisible(true);
  }, [setChannelOptionsBottomSheetVisible]);

  const handleConfirmBlockCustomer = useCallback(async () => {
    try {
      if (!channel?.data?.correlationId) {
        throw new Error('Correlation ID not found');
      }

      const consumerId = getConsumerIdFromChannel(channel);

      if (!companyId) {
        throw new Error('Company ID not found');
      }

      if (!consumerId) {
        throw new Error('Consumer ID not found');
      }

      await tradeAppBff.accounts.blockCustomer(
        channel.data.correlationId,
        consumerId,
        companyId,
      );
      navigation.goBack();

      showToast({
        type: 'success',
        text1: 'Customer has been reported.',
      });
    } catch (e) {
      captureException(e, {
        source: 'handleBlockCustomer',
      });

      showToast({
        type: 'error',
        text1: 'Unable to block customer. Please try again.',
      });
    } finally {
      setReportCustomerConfirmationBottomSheetVisible(false);
    }
  }, [channel, companyId, navigation]);

  const handleDismissChannelOptionsBottomSheet = useCallback(() => {
    setChannelOptionsBottomSheetVisible(false);
  }, [setChannelOptionsBottomSheetVisible]);

  const handleDismissReportConfirmationBottomSheet = useCallback(() => {
    setReportCustomerConfirmationBottomSheetVisible(false);
  }, [setReportCustomerConfirmationBottomSheetVisible]);

  const handleAppointmentOptionsDismiss = useCallback(() => {
    setAppointmentOptions(null);
  }, [setAppointmentOptions]);

  const handleQuotesOptionsDismiss = useCallback(() => {
    setQuoteMessage(null);
    setQuoteOptionsBottomSheetVisible(false);
  }, [setQuoteMessage, setQuoteOptionsBottomSheetVisible]);

  const handleInvoiceOptionsDismiss = useCallback(() => {
    setInvoiceMessage(null);
    setInvoiceOptionsBottomSheetVisible(false);
  }, [setInvoiceMessage, setInvoiceOptionsBottomSheetVisible]);

  const handleJobDetailsPress = useCallback(() => {
    if (isTruthy(job)) {
      navigation.navigate(MARKETPLACE_JOB_DETAILS_SCREEN, { id: job.id });
      return;
    }
  }, [job, navigation]);

  const handleAppointmentsPress = useCallback(() => {
    if (isTruthy(channel)) {
      navigation.navigate(CHANNEL_APPOINTMENTS_SCREEN, {
        channelId: channel.cid,
      });
    }
  }, [channel, navigation]);

  const getChatInputComponent = useCallback(
    (props: ChatInputProps) => {
      return <ChatInput {...props} job={job} ref={chatInputRef} />;
    },
    [job],
  );

  const getSystemMessageComponent = useCallback(
    (props: SmartMessageProps) => {
      return <SmartMessage {...props} job={job} />;
    },
    [job],
  );

  const isChannelFrozen = useMemo(() => channel?.data?.frozen, [channel]);

  // Effects

  useSetChannel(channelId);

  useEffect(() => {
    if (isTruthy(channel) && isTruthy(job)) {
      logEvent(EVENT_TYPE.INBOX_CHANNEL_OPEN, {
        channelId: channel.cid,
        correlationId: channel.data?.correlationId,
        jobId: job.id,
      });
    }
  }, [channel, channel?.cid, channel?.data?.correlationId, job, job?.id]);

  useTrackChatIssues({
    screen: CHANNEL_SCREEN,
    isDisabled: isTruthy(channel && job),
    context: {
      companyId,
      channelId: channelId,
      jobId: job?.id,
      channelLoaded: isTruthy(channel),
      jobLoaded: isTruthy(job),
      isChannelFrozen,
      streamState: state,
      jobError: jobError?.message ?? null,
      correlationId: channel?.data?.correlationId,
    },
  });

  useEffect(() => {
    if (channel && job) {
      setIsInitialLoad(false);
    }
  }, [channel, job]);

  // Renderers
  const renderLoading = useMemo(
    () => (
      <View style={styles.loaderContainer}>
        <Spinner size={48} />
      </View>
    ),
    [],
  );

  if (!channel) {
    return renderLoading;
  }

  return (
    <OverlayProvider value={{ style: theme }} bottomInset={bottom}>
      <Chat client={client}>
        <KeyboardAvoidingView
          behavior="padding"
          keyboardVerticalOffset={-bottom}
          style={styles.contentWrapper}
        >
          <View style={[styles.contentWrapper, { paddingBottom: bottom }]}>
            {isTruthy(job) && state === StreamConnectionState.Connected ? (
              <Channel<ChatGenerics>
                key={channel.cid}
                channel={channel}
                messageActions={messageActions}
                MessageAvatar={() => null}
                myMessageTheme={myMessageTheme}
                keyboardVerticalOffset={headerHeight}
                disableKeyboardCompatibleView
                Input={getChatInputComponent}
                MessageSystem={getSystemMessageComponent}
                NetworkDownIndicator={() => null}
              >
                <View style={styles.contentWrapper}>
                  <ChannelJobDetailsHeader
                    job={job}
                    onJobDetailsPress={handleJobDetailsPress}
                    onAppointmentsPress={handleAppointmentsPress}
                  />
                  <MessageList
                    HeaderComponent={() => {
                      if (isInitialLoad) {
                        return null;
                      }

                      return !hasRespondedToCustomer &&
                        isJobAccepted(job.status) ? (
                        <QuickActionsMessage
                          onRequestMoreInfo={
                            quickActionHandlers.handleRequestMoreInfo
                          }
                          onRequestPhotos={
                            quickActionHandlers.handleRequestPhotos
                          }
                          onRequestAddress={
                            quickActionHandlers.handleRequestAddress
                          }
                          onBookAppointment={
                            quickActionHandlers.handleBookAppointment
                          }
                          onCreateQuote={quickActionHandlers.handleCreateQuote}
                          onCreatePaymentRequest={
                            quickActionHandlers?.handleCreatePaymentRequest
                          }
                        />
                      ) : null;
                    }}
                  />
                  {isJobOffered(job.status) && (
                    <InterestedInJobCTA jobId={job.id} />
                  )}

                  {isJobAccepted(job.status) && (
                    <View>
                      {hasRespondedToCustomer && (
                        <QuickActionsChatInput
                          onRequestMoreInfo={quickActions.handleRequestMoreInfo}
                          onRequestPhotos={
                            quickActionHandlers.handleRequestPhotos
                          }
                          onRequestAddress={
                            quickActionHandlers.handleRequestAddress
                          }
                          onBookAppointment={
                            quickActionHandlers.handleBookAppointment
                          }
                          onCreateQuote={quickActionHandlers.handleCreateQuote}
                          onCreatePaymentRequest={
                            quickActionHandlers?.handleCreatePaymentRequest
                          }
                        />
                      )}
                      <MessageInput />
                    </View>
                  )}
                  {isJobDeclined(job.status) && <ChannelFrozen jobDeclined />}
                </View>
              </Channel>
            ) : (
              renderLoading
            )}
          </View>
        </KeyboardAvoidingView>
      </Chat>
      <ChannelOptionsBottomSheet
        job={job}
        onBlockCustomer={handleBlockCustomer}
        onDismiss={handleDismissChannelOptionsBottomSheet}
        visible={channelOptionsBottomSheetVisible}
      />
      <AppointmentOptionsBottomSheet
        job={job}
        appointment={appointmentOptions}
        onDismiss={handleAppointmentOptionsDismiss}
      />
      <QuoteBottomSheet
        message={quoteMessage}
        onDismiss={handleQuotesOptionsDismiss}
        visible={quoteOptionsBottomSheetVisible}
      />
      <ReportCustomerConfirmationBottomSheet
        visible={reportCustomerConfirmationBottomSheetVisible}
        onDismiss={handleDismissReportConfirmationBottomSheet}
        onConfirm={handleConfirmBlockCustomer}
      />
      <InvoiceBottomSheet
        message={invoiceMessage}
        onDismiss={handleInvoiceOptionsDismiss}
        visible={invoiceOptionsBottomSheetVisible}
      />
      <CancelAppointmentBottomSheet appointmentId={cancelAppointmentId} />
      <ChatGuide {...chatGuideProps} />
      <JobPaymentsGuide {...jobPaymentsGuideProps} />
    </OverlayProvider>
  );
};

const styles = createMortarStyles(() => ({
  contentWrapper: {
    flex: 1,
    position: 'relative',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
}));

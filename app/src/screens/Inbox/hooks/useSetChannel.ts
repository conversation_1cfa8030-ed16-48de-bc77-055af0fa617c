import { use<PERSON>tom } from 'jotai';
import { useEffect } from 'react';
import { useStreamManager } from 'src/services/stream';
import { activeChannelAtom } from '../state/channelState';

export const useSetChannel = (channelId?: string): undefined => {
  const [channel, setChannel] = useAtom(activeChannelAtom);
  const { client } = useStreamManager();

  useEffect(() => {
    if (!channelId) {
      return;
    }

    // Stream SDK expects the channel ID to be prefixed with 'messaging:'
    // but doesn't return it that way via webhooks which is used for push notifications
    const formattedChannelId = `messaging:${channelId?.replace('messaging:', '')}`;

    if (formattedChannelId === channel?.cid) {
      return;
    }

    client?.queryChannels({ cid: formattedChannelId }).then((channels) => {
      const [matchedChannel] = channels ?? [];
      setChannel(matchedChannel);
    });
  }, [client, channel, channelId, setChannel]);
};

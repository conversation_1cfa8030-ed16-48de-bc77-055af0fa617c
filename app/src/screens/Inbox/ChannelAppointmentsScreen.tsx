import React, { useCallback, useMemo } from 'react';
import { View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { isAfter, isBefore } from 'date-fns';
import { useAtom } from 'jotai';
import {
  createMortarStyles,
  createTestIds,
  isTruthy,
} from '@cat-home-experts/react-native-utilities';
import {
  AppointmentMessage,
  Button,
  CalendarEventStatus,
  Typography,
} from '@cat-home-experts/react-native-components';
import { getAcceptedAlternative } from 'src/services/calendar/utils';
import { ContentSegment } from 'src/components/ContentSegment';
import { JobAppointmentType } from 'src/data/schemas/api/capi/appointments';
import {
  useMarketplaceJobAppointments,
  useMarketplaceJobDetails,
} from 'src/screens/MarketplaceJobs/hooks';
import {
  ADD_APPOINTMENT_SCREEN,
  CHANNEL_APPOINTMENTS_SCREEN,
  IS_WEB,
  UPDATE_APPOINTMENT_SCREEN,
} from 'src/constants';
import type { AllScreensParamList } from 'src/navigation/routes';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { useSetChannel } from './hooks/useSetChannel';
import {
  activeChannelAtom,
  appointmentOptionsAtom,
  cancelAppointmentIdAtom,
} from './state/channelState';
import { ChannelAppointmentMessage } from './types';
import { AppointmentOptionsBottomSheet } from './components/Appointments/AppointmentOptionsBottomSheet';
import { CancelAppointmentBottomSheet } from './components/Appointments/CancelAppointmentBottomSheet';

type ExtendedCalendarEventStatus = CalendarEventStatus | 'EXPIRED';
type GroupedAppointmentsMapItem = {
  key: ExtendedCalendarEventStatus;
  title: string;
  subtitle?: string;
  appointments?: JobAppointmentType[];
};

const TEST_IDS = createTestIds('channel-appointments-screen', {
  APPOINTMENT_GROUPS: 'appointment-groups',
  APPOINTMENT_GROUP: 'appointment-group',
  APPOINTMENT_GROUP_TITLE: 'appointment-group-title',
  APPOINTMENT_GROUP_APPOINTMENTS: 'appointment-group-appointments',
  BOOK_APPOINTMENT_BUTTON: 'book-appointment-button',
  APPOINTMENT_WARNING: 'appointment-warning',
});

interface ChannelAppointmentsScreenProps {}

export const ChannelAppointmentsScreen: React.NativeFC<
  ChannelAppointmentsScreenProps,
  typeof TEST_IDS
> = () => {
  const { bottom } = useSafeAreaInsets();
  const { navigate } = useNavigation();
  const [channel] = useAtom(activeChannelAtom);
  const [cancelAppointmentId] = useAtom(cancelAppointmentIdAtom);
  const [appointmentOptions, setAppointmentOptions] = useAtom(
    appointmentOptionsAtom,
  );

  const route =
    useRoute<
      RouteProp<AllScreensParamList, typeof CHANNEL_APPOINTMENTS_SCREEN>
    >();
  const { channelId } = route.params ?? {};
  if (!channelId) {
    throw new InvalidNavParamsError();
  }

  const { job } = useMarketplaceJobDetails(channel?.data?.correlationId);
  const { appointments } = useMarketplaceJobAppointments(
    channel?.data?.correlationId,
  );

  useSetChannel(channelId);

  const groupedAppointments = useMemo(
    () =>
      appointments?.reduce(
        (acc, appointment) => {
          let status = appointment.status as ExtendedCalendarEventStatus;
          if (status === CalendarEventStatus.CREATED) {
            const allAlternativesInPast = appointment.alternatives.every(
              (alt) => isBefore(alt.end || alt.start, new Date()),
            );

            if (allAlternativesInPast) {
              status = 'EXPIRED';
            }
          }

          if (!acc[status]) {
            acc[status] = [];
          }

          acc[status].push(appointment);

          return acc;
        },
        {} as Record<ExtendedCalendarEventStatus, JobAppointmentType[]>,
      ),
    [appointments],
  );

  const groupedAppointmentsMap: GroupedAppointmentsMapItem[] = useMemo(
    () =>
      [
        {
          key: 'EXPIRED',
          title: 'Expired',
          subtitle:
            'Your appointment times are now in the past - try suggesting some new options',
          appointments: groupedAppointments?.EXPIRED,
        },
        {
          key: 'CREATED',
          title: 'Pending',
          subtitle: `${groupedAppointments?.CREATED?.length || 0} appointment${(groupedAppointments?.CREATED?.length || 0) === 1 ? '' : 's'} awaiting ${job?.consumer.firstName || 'the consumer'}’s response`,
          appointments: groupedAppointments?.CREATED,
        },
        {
          key: 'ACCEPTED',
          title: 'Confirmed',
          appointments: groupedAppointments?.ACCEPTED,
        },
        {
          key: 'CANCELLED',
          title: 'Cancelled',
          appointments: groupedAppointments?.CANCELLED,
        },
        {
          key: 'REJECTED',
          title: 'Rejected',
          appointments: groupedAppointments?.REJECTED,
        },
      ].filter((group) =>
        isTruthy(group.appointments?.length),
      ) as GroupedAppointmentsMapItem[],
    [groupedAppointments, job],
  );

  const enableOnPress = useCallback((appointment: JobAppointmentType) => {
    if (
      appointment.status === 'CANCELLED' ||
      appointment.status === 'REJECTED'
    ) {
      return false;
    }

    if (appointment.status === 'ACCEPTED') {
      const acceptedAlternative = getAcceptedAlternative(appointment);
      if (!acceptedAlternative) {
        return false;
      }

      return isAfter(
        acceptedAlternative.end || acceptedAlternative.start,
        new Date(),
      );
    }

    return true;
  }, []);

  const onPress = useCallback(
    (appointment: JobAppointmentType) => () => {
      if (appointment.status === 'ACCEPTED') {
        const options: ChannelAppointmentMessage = {
          channelId,
          appointmentId: appointment.id,
          appointmentDetails: {
            id: appointment.id,
            title: appointment.type.title,
            status: CalendarEventStatus.ACCEPTED,
            alternatives: appointment.alternatives,
          },
        };

        setAppointmentOptions(options);
      }

      if (appointment.status === 'CREATED') {
        navigate(UPDATE_APPOINTMENT_SCREEN, {
          channelId,
          appointmentId: appointment.id,
          rescheduleRequestedByTrade: true,
        });
      }
    },
    [channelId, setAppointmentOptions, navigate],
  );

  const handleAppointmentOptionsDismiss = useCallback(() => {
    setAppointmentOptions(null);
  }, [setAppointmentOptions]);

  const handleBookAppointmentPress = useCallback(() => {
    if (isTruthy(job)) {
      navigate(ADD_APPOINTMENT_SCREEN, { channelId });
    }
  }, [navigate, job, channelId]);

  const isCreateAppointmentButtonDisabled = useMemo(
    () =>
      isTruthy(groupedAppointments?.CREATED) ||
      isTruthy(groupedAppointments?.EXPIRED),
    [groupedAppointments],
  );

  const appointmentWarningText = useMemo(() => {
    if (isTruthy(groupedAppointments?.EXPIRED)) {
      return `You can't book another appointment until you cancel any Expired ones or offer new options in the future and ${job?.consumer.firstName || 'the consumer'} has confirmed a date and time`;
    }

    return `You can't book another appointment until ${job?.consumer.firstName || 'the consumer'} has confirmed a date and time for your current appointment invitation`;
  }, [groupedAppointments, job?.consumer.firstName]);

  return (
    <>
      <ScrollView
        testID={TEST_IDS.ROOT}
        style={styles.root}
        contentContainerStyle={[
          styles.contentContainer,
          { paddingBottom: bottom },
        ]}
      >
        <ContentSegment
          style={styles.contentWrapper}
          testID={TEST_IDS.APPOINTMENT_GROUPS}
        >
          {groupedAppointmentsMap?.map((group) => (
            <View key={group.key} testID={TEST_IDS.APPOINTMENT_GROUP}>
              <Typography
                useVariant="bodySemiBold"
                testID={TEST_IDS.APPOINTMENT_GROUP_TITLE}
              >
                {`${group.title} (${group.appointments?.length || 0})`}
              </Typography>
              {isTruthy(group.subtitle) && (
                <Typography style={styles.subtitle}>
                  {group.subtitle}
                </Typography>
              )}
              <View
                style={styles.appointments}
                testID={TEST_IDS.APPOINTMENT_GROUP_APPOINTMENTS}
              >
                {group.appointments?.map((appointment) => (
                  <View key={appointment.id}>
                    <AppointmentMessage
                      title={appointment.type.title}
                      dateRanges={appointment.alternatives}
                      status={appointment.status}
                      onPress={
                        enableOnPress(appointment)
                          ? onPress(appointment)
                          : undefined
                      }
                    />
                  </View>
                ))}
              </View>
            </View>
          ))}
          <View style={styles.buttonWrapper}>
            <Button
              label="Book an appointment"
              onPress={handleBookAppointmentPress}
              block
              variant="outlineSecondary"
              isDisabled={isCreateAppointmentButtonDisabled}
              testID={TEST_IDS.BOOK_APPOINTMENT_BUTTON}
            />
            {isCreateAppointmentButtonDisabled && (
              <Typography
                useVariant="bodySMRegular"
                style={styles.appointmentWarning}
                testID={TEST_IDS.APPOINTMENT_WARNING}
              >
                {appointmentWarningText}
              </Typography>
            )}
          </View>
        </ContentSegment>
      </ScrollView>
      <AppointmentOptionsBottomSheet
        job={job}
        appointment={appointmentOptions}
        onDismiss={handleAppointmentOptionsDismiss}
      />
      <CancelAppointmentBottomSheet appointmentId={cancelAppointmentId} />
    </>
  );
};

ChannelAppointmentsScreen.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  root: {
    flexGrow: 1,
    flexShrink: IS_WEB ? 1 : 0,
    paddingTop: IS_WEB ? spacing(1) : 0,
  },
  contentContainer: {
    width: '100%',
    maxWidth: spacing(100),
    marginHorizontal: 'auto',
  },
  contentWrapper: {
    gap: spacing(3),
    borderTopWidth: IS_WEB ? 1 : 0,
    borderBottomWidth: IS_WEB ? 1 : 0,
  },
  subtitle: {
    marginVertical: spacing(1),
    color: palette.mortarV3.tokenNeutral600,
  },
  appointments: {
    marginTop: spacing(1),
    gap: spacing(2),
  },
  buttonWrapper: {
    gap: spacing(1),
  },
  appointmentWarning: {
    color: palette.mortarV3.tokenNeutral600,
  },
}));

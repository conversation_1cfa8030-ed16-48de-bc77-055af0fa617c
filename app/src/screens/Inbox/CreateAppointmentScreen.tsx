import React, { useCallback } from 'react';
import { useAtom } from 'jotai';
import { ScrollView } from 'react-native-gesture-handler';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { showToast } from 'src/components';
import { useMarketplaceJobDetails } from 'src/screens/MarketplaceJobs/hooks/useMarketplaceJobDetails';
import { captureException } from 'src/services/datadog';
import { useUserContext } from 'src/hooks/useUser';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { ADD_APPOINTMENT_SCREEN, IS_WEB } from 'src/constants';
import type { AllScreensParamList } from 'src/navigation/routes';
import { activeChannelAtom } from './state/channelState';
import {
  AppointmentForm,
  AppointmentFormProps,
} from './components/Appointments/AppointmentForm';
import { getChannelConsumerMember } from './utils';
import { useAppointment } from './hooks/useAppointment';
import { useSetChannel } from './hooks/useSetChannel';

export const CreateAppointmentScreen: React.FC = () => {
  const { companyId } = useUserContext();
  const [channel] = useAtom(activeChannelAtom);
  const { job } = useMarketplaceJobDetails(channel?.data?.correlationId);
  const { goBack } = useNavigation();
  const { createAppointment } = useAppointment();
  const route =
    useRoute<RouteProp<AllScreensParamList, typeof ADD_APPOINTMENT_SCREEN>>();

  useSetChannel(route.params?.channelId);

  const handleSend: AppointmentFormProps['onSend'] = useCallback(
    async (appointmentDetails) => {
      try {
        if (!job?.id) {
          throw new Error('Missing job data');
        }

        if (!channel) {
          throw new Error('Missing channel data');
        }

        if (!companyId) {
          throw new Error('Missing company data');
        }

        // TODO: Get consumerId from new jobs service when available
        const consumer = await getChannelConsumerMember(channel, companyId);

        if (!consumer?.user_id) {
          throw new Error('Consumer details not found');
        }

        await createAppointment({
          jobId: job.id,
          consumerId: consumer.user_id,
          type: appointmentDetails.type,
          alternatives: appointmentDetails.alternatives,
        });

        logEvent(EVENT_TYPE.APPOINTMENT_CREATED);

        showToast({
          text1: `Appointment times have been offered to ${job.consumer.firstName || 'the customer'}`,
          type: 'success',
        });

        goBack();
      } catch (e) {
        showToast({
          text1: 'Unable to send appointment times, please try again',
          type: 'error',
        });
        captureException(e, {
          tags: {
            module: 'CreateAppointmentScreen',
            method: 'handleSend',
          },
        });
      }
    },
    [createAppointment, goBack, job, channel, companyId],
  );

  return (
    <ScrollView
      style={styles.root}
      contentContainerStyle={styles.contentContainer}
    >
      <AppointmentForm onSend={handleSend} />
    </ScrollView>
  );
};

const styles = createMortarStyles(({ spacing, palette }) => ({
  root: {
    flexGrow: 1,
    flexShrink: IS_WEB ? 1 : 0,
    backgroundColor: IS_WEB
      ? palette.mortar.tokenColorLightBlue
      : palette.mortarV3.tokenNeutral100,
    paddingTop: IS_WEB ? spacing(1) : 0,
  },
  contentContainer: {
    width: '100%',
    maxWidth: spacing(100),
    marginHorizontal: 'auto',
  },
}));

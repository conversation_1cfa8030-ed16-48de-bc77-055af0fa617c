import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { View, ScrollView } from 'react-native';
import { Channel, Chat, MessageInput, MessageList } from 'stream-chat-react';
import { useAtom } from 'jotai';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  createMortarStyles,
  isTruthy,
} from '@cat-home-experts/react-native-utilities';
import { useStreamManager } from 'src/services/stream';
import {
  CHANNEL_APPOINTMENTS_SCREEN,
  CHANNEL_SCREEN,
  MARKETPLACE_JOB_DETAILS_SCREEN,
} from 'src/constants';

import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { Spinner } from 'src/components/primitives/Spinner';
import { useHasRespondedToCustomer } from 'src/services/stream/hooks/useHasRespondedToCustomer';
import { DroppableProvider } from 'src/components/Droppable';
import {
  isJobAccepted,
  isJobDeclined,
  isJobOffered,
} from 'src/screens/MarketplaceJobs/utilities';
import { showToast } from 'src/components';
import { captureException } from 'src/services/datadog';
import { useMarketplaceJobDetails } from 'src/screens/MarketplaceJobs/hooks/useMarketplaceJobDetails';
import { useUserContext } from 'src/hooks/useUser';
import {
  QuickActionsChatInput,
  QuickActionsMessage,
} from 'src/components/QuickActions';
import { useQuickActions } from 'src/hooks/useQuickActions/useQuickActions';
import { ChatGenerics } from 'src/types';
import { tradeAppBff } from 'src/data/api/trade-app-bff';
import { SmartMessageType } from 'src/services/stream/smartMessages';
import type { AllScreensParamList } from 'src/navigation/routes';
import {
  quoteMessageAtom,
  quoteOptionsBottomSheetVisibleAtom,
  appointmentOptionsAtom,
  cancelAppointmentIdAtom,
  channelOptionsBottomSheetVisibleAtom,
  invoiceOptionsBottomSheetVisibleAtom,
  invoiceMessageAtom,
  activeChannelAtom,
} from './state/channelState';
import { ChannelJobDetailsHeader } from './components/ChannelJobDetailsHeader';
import { ChannelOptionsBottomSheet } from './components/ChannelOptionsBottomSheet';
import { ChatInput, ChatInputRef } from './components/ChatInput';
import { ChatGuide } from './components/ChatGuide';
import { JobPaymentsGuide } from '../JobPayments/components/JobPaymentsGuide';
import { InterestedInJobCTA } from './components/InterestedInJobCTA';
import { SmartMessage, SmartMessageProps } from './components/SmartMessages';
import { AppointmentOptionsBottomSheet } from './components/Appointments/AppointmentOptionsBottomSheet';
import { CancelAppointmentBottomSheet } from './components/Appointments/CancelAppointmentBottomSheet';
import { QuoteBottomSheet } from './components/Quotes/QuoteBottomSheet';
import { InvoiceBottomSheet } from './components/Quotes/InvoiceBottomSheet';
import { WebAttachment } from './components/Web/WebAttachment';
import { useChatGuide } from './hooks/useChatGuide';
import { useTrackChatIssues } from './hooks/useTrackChatIssues';
import { getConsumerIdFromChannel } from './utils';
import { ChatInputProps } from './types';
import { useSetChannel } from './hooks/useSetChannel';
import { ChannelFrozen } from './components/ChannelFrozen';
import { useChatJobPaymentsGuide } from './hooks/useChatJobPaymentsGuide';
import { ReportCustomerConfirmationBottomSheet } from './components/ReportCustomerConfirmationBottomSheet';

export const ChannelScreen: React.FC = () => {
  // Global State
  const [channel] = useAtom(activeChannelAtom);
  const [appointmentOptions, setAppointmentOptions] = useAtom(
    appointmentOptionsAtom,
  );
  const [cancelAppointmentId] = useAtom(cancelAppointmentIdAtom);
  const [
    channelOptionsBottomSheetVisible,
    setChannelOptionsBottomSheetVisible,
  ] = useAtom(channelOptionsBottomSheetVisibleAtom);
  const [quoteMessage, setQuoteMessage] = useAtom(quoteMessageAtom);
  const [quoteOptionsBottomSheetVisible, setQuoteOptionsBottomSheetVisible] =
    useAtom(quoteOptionsBottomSheetVisibleAtom);
  const [invoiceMessage, setInvoiceMessage] = useAtom(invoiceMessageAtom);
  const [
    invoiceOptionsBottomSheetVisible,
    setInvoiceOptionsBottomSheetVisible,
  ] = useAtom(invoiceOptionsBottomSheetVisibleAtom);
  const [
    reportCustomerConfirmationBottomSheetVisible,
    setReportCustomerConfirmationBottomSheetVisible,
  ] = useState(false);

  // Computed Values
  const { bottom } = useSafeAreaInsets();
  const navigation = useNavigation();
  const { companyId } = useUserContext();
  const route =
    useRoute<RouteProp<AllScreensParamList, typeof CHANNEL_SCREEN>>();
  const { channelId } = route.params ?? {};
  const { client, state } = useStreamManager();
  const { job, jobError } = useMarketplaceJobDetails(
    channel?.data?.correlationId,
  );
  const chatGuideProps = useChatGuide();
  const jobPaymentsGuideProps = useChatJobPaymentsGuide();
  const chatInputRef = useRef<ChatInputRef>(null);
  const isChannelFrozen = useMemo(() => channel?.data?.frozen, [channel]);

  const quickActions = useQuickActions(channel, job, chatInputRef);
  const { hasRespondedToCustomer, ...quickActionHandlers } = quickActions;

  // Methods
  const handleBlockCustomer = useCallback(async () => {
    setChannelOptionsBottomSheetVisible(false);
    setReportCustomerConfirmationBottomSheetVisible(true);
  }, [setChannelOptionsBottomSheetVisible]);

  const handleConfirmBlockCustomer = useCallback(async () => {
    try {
      if (!channel?.data?.correlationId) {
        throw new Error('Correlation ID not found');
      }

      const consumerId = getConsumerIdFromChannel(channel);

      if (!companyId) {
        throw new Error('Company ID not found');
      }

      if (!consumerId) {
        throw new Error('Consumer ID not found');
      }

      await tradeAppBff.accounts.blockCustomer(
        channel.data.correlationId,
        consumerId,
        companyId,
      );
      navigation.goBack();

      showToast({
        type: 'success',
        text1: 'Customer has been blocked.',
      });
    } catch (e) {
      captureException(e, {
        source: 'handleBlockCustomer',
      });

      showToast({
        type: 'error',
        text1: 'Unable to block customer. Please try again.',
      });
    } finally {
      setChannelOptionsBottomSheetVisible(false);
    }
  }, [channel, companyId, navigation, setChannelOptionsBottomSheetVisible]);

  const handleDismissChannelOptionsBottomSheet = useCallback(() => {
    setChannelOptionsBottomSheetVisible(false);
  }, [setChannelOptionsBottomSheetVisible]);

  const handleDismissReportConfirmationBottomSheet = useCallback(() => {
    setReportCustomerConfirmationBottomSheetVisible(false);
  }, [setReportCustomerConfirmationBottomSheetVisible]);

  const handleAppointmentOptionsDismiss = useCallback(() => {
    setAppointmentOptions(null);
  }, [setAppointmentOptions]);

  const handleQuotesOptionsDismiss = useCallback(() => {
    setQuoteMessage(null);
    setQuoteOptionsBottomSheetVisible(false);
  }, [setQuoteMessage, setQuoteOptionsBottomSheetVisible]);

  const handleInvoiceOptionsDismiss = useCallback(() => {
    setInvoiceMessage(null);
    setInvoiceOptionsBottomSheetVisible(false);
  }, [setInvoiceMessage, setInvoiceOptionsBottomSheetVisible]);

  const handleJobDetailsPress = useCallback(() => {
    if (isTruthy(job)) {
      navigation.navigate(MARKETPLACE_JOB_DETAILS_SCREEN, { id: job.id });
      return;
    }
  }, [job, navigation]);

  const handleAppointmentsPress = useCallback(() => {
    if (isTruthy(channel)) {
      navigation.navigate(CHANNEL_APPOINTMENTS_SCREEN, {
        channelId: channel.cid,
      });
    }
  }, [channel, navigation]);

  // Effects
  useHasRespondedToCustomer(channel);
  useSetChannel(channelId);

  useEffect(() => {
    if (
      isTruthy(channel) &&
      isTruthy(job) &&
      isTruthy(channel?.cid) &&
      isTruthy(job?.id)
    ) {
      logEvent(EVENT_TYPE.INBOX_CHANNEL_OPEN, {
        channelId: channel.cid,
        correlationId: channel.data?.correlationId,
        jobId: job.id,
      });
    }
  }, [channel, channel?.cid, channel?.data?.correlationId, job, job?.id]);

  useTrackChatIssues({
    screen: CHANNEL_SCREEN,
    isDisabled: isTruthy(channel && job),
    context: {
      companyId,
      channelId: channelId,
      jobId: job?.id,
      channelLoaded: isTruthy(channel),
      jobLoaded: isTruthy(job),
      isChannelFrozen,
      streamState: state,
      jobError: jobError?.message ?? null,
      correlationId: channel?.data?.correlationId,
    },
  });

  // Renderers
  const renderLoading = useMemo(
    () => (
      <View style={styles.loaderContainer}>
        <Spinner size={48} />
      </View>
    ),
    [],
  );

  const getChatInputComponent = useCallback(
    (props: ChatInputProps) => {
      return (
        <>
          {hasRespondedToCustomer && isJobAccepted(job?.status) && (
            <View style={styles.quickActionsInputContainer}>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <QuickActionsChatInput
                  onRequestMoreInfo={quickActionHandlers.handleRequestMoreInfo}
                  onRequestPhotos={quickActionHandlers.handleRequestPhotos}
                  onRequestAddress={quickActionHandlers.handleRequestAddress}
                  onBookAppointment={quickActionHandlers.handleBookAppointment}
                  onCreateQuote={quickActionHandlers.handleCreateQuote}
                  onCreatePaymentRequest={
                    quickActionHandlers?.handleCreatePaymentRequest
                  }
                />
              </ScrollView>
            </View>
          )}
          <ChatInput {...props} job={job} ref={chatInputRef} />
        </>
      );
    },
    [job, hasRespondedToCustomer, quickActionHandlers],
  );

  const getSystemMessageComponent = useCallback(
    (props: SmartMessageProps) => {
      return (
        <>
          {!hasRespondedToCustomer &&
          job &&
          isJobAccepted(job?.status) &&
          props.message.smartType === SmartMessageType.JOB_ACCEPTED ? (
            <View style={styles.quickActionsContainer}>
              <QuickActionsMessage
                onRequestMoreInfo={quickActionHandlers.handleRequestMoreInfo}
                onRequestPhotos={quickActionHandlers.handleRequestPhotos}
                onRequestAddress={quickActionHandlers.handleRequestAddress}
                onBookAppointment={quickActionHandlers.handleBookAppointment}
                onCreateQuote={quickActionHandlers.handleCreateQuote}
                onCreatePaymentRequest={
                  quickActionHandlers?.handleCreatePaymentRequest
                }
              />
            </View>
          ) : (
            <SmartMessage {...props} job={job} />
          )}
        </>
      );
    },
    [job, hasRespondedToCustomer, quickActionHandlers],
  );

  if (!channel) {
    return renderLoading;
  }

  return (
    <DroppableProvider>
      <View style={styles.root}>
        <Chat client={client}>
          <View style={[styles.contentWrapper, { paddingBottom: bottom }]}>
            {isTruthy(job) ? (
              <Channel<ChatGenerics>
                key={channel.cid}
                channel={channel}
                Input={getChatInputComponent}
                Attachment={WebAttachment}
                MessageSystem={getSystemMessageComponent}
              >
                <View style={styles.contentWrapper}>
                  <ChannelJobDetailsHeader
                    job={job}
                    onJobDetailsPress={handleJobDetailsPress}
                    onAppointmentsPress={handleAppointmentsPress}
                  />
                  <View style={styles.contentWrapper}>
                    <MessageList />
                  </View>
                  {isJobOffered(job.status) && (
                    <InterestedInJobCTA jobId={job.id} />
                  )}
                  {isJobAccepted(job.status) && <MessageInput />}
                  {isJobDeclined(job.status) && <ChannelFrozen jobDeclined />}
                </View>
              </Channel>
            ) : (
              renderLoading
            )}
          </View>
        </Chat>
        <QuoteBottomSheet
          message={quoteMessage}
          onDismiss={handleQuotesOptionsDismiss}
          visible={quoteOptionsBottomSheetVisible}
        />
        <InvoiceBottomSheet
          message={invoiceMessage}
          onDismiss={handleInvoiceOptionsDismiss}
          visible={invoiceOptionsBottomSheetVisible}
        />
        <ChannelOptionsBottomSheet
          job={job}
          onBlockCustomer={handleBlockCustomer}
          onDismiss={handleDismissChannelOptionsBottomSheet}
          visible={channelOptionsBottomSheetVisible}
        />
        <ReportCustomerConfirmationBottomSheet
          visible={reportCustomerConfirmationBottomSheetVisible}
          onDismiss={handleDismissReportConfirmationBottomSheet}
          onConfirm={handleConfirmBlockCustomer}
        />
        <AppointmentOptionsBottomSheet
          job={job}
          appointment={appointmentOptions}
          onDismiss={handleAppointmentOptionsDismiss}
        />
        <CancelAppointmentBottomSheet appointmentId={cancelAppointmentId} />
        <ChatGuide {...chatGuideProps} />
        <JobPaymentsGuide {...jobPaymentsGuideProps} />
      </View>
    </DroppableProvider>
  );
};

const styles = createMortarStyles(({ palette }) => ({
  root: {
    flex: 1,
  },
  contentWrapper: {
    flex: 1,
    position: 'relative',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quickActionsContainer: {
    maxWidth: 700,
    width: '100%',
    marginLeft: 'auto',
    marginRight: 'auto',
    alignItems: 'center',
  },
  quickActionsInputContainer: {
    width: '100%',
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    maxHeight: 100,
    overflow: 'hidden',
  },
}));

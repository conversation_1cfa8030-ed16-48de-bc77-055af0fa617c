import { cleanup, render } from '@testing-library/react-native';
import React from 'react';
import {
  MY_TEAM_TRADE_ACCREDITATIONS_CREATE_SCREEN,
  MY_TEAM_TRADE_ACCREDITATIONS_UPDATE_SCREEN,
} from 'src/constants';
import { AccreditationResponseType } from 'src/data/schemas/api/trade-app-bff/person/PersonAccreditationResponse';
import { useUserContext } from 'src/hooks/useUser';
import { AccreditationStatus } from 'src/screens/Accreditations/constants';
import { createQueryClientWrapper } from 'src/utilities/tanstack-query/tanstack-test-utils';
import { TradeAccreditations } from './TradeAccreditations';
import { useGetTradeAccreditations } from './hooks/useGetTradeAccreditations';
import { useMyTeamRoute } from './hooks/useMyTeamRoute';

jest.mock('./hooks/useGetTradeAccreditations', () => ({
  useGetTradeAccreditations: jest.fn(),
}));

jest.mock('./hooks/useMyTeamRoute', () => ({
  useMyTeamRoute: jest.fn(),
}));

jest.mock('src/hooks/useUser', () => ({
  useUserContext: jest.fn(),
}));

jest.mock('src/services/analytics', () => ({
  logEvent: jest.fn(),
}));

jest.mock('src/screens/Accreditations/shared', () => ({
  AccreditationList: jest.fn(({ onAddButtonPress, onItemPress }) => {
    if (onAddButtonPress && typeof onAddButtonPress === 'function') {
      onAddButtonPress();
    }

    if (onItemPress && typeof onItemPress === 'function') {
      onItemPress(123);
    }

    return null;
  }),
}));

const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => {
  const actualNav = jest.requireActual('@react-navigation/native');
  return {
    ...actualNav,
    useFocusEffect: jest.fn((callback) => callback()),
    useNavigation: () => ({ navigate: mockNavigate }),
  };
});

const mockAccreditations: AccreditationResponseType[] = [
  {
    id: '123456',
    accreditationId: 123,
    personId: '99999',
    companyId: 1234,
    accreditationName: 'Test Accreditation',
    accreditationLogo: 'test-logo.png',
    status: AccreditationStatus.PendingReview,
    proof: [],
    canExpire: true,
    statusDate: new Date(),
    statusText: 'Pending Review',
  },
];

const renderTradeAccreditations = () => {
  const wrapper = createQueryClientWrapper();
  return render(<TradeAccreditations />, { wrapper });
};

describe('screens | MyTeam | TradeAccreditations', () => {
  beforeEach(() => {
    (useMyTeamRoute as jest.Mock).mockReturnValue({
      params: {
        personId: '99999',
      },
    });

    (useUserContext as jest.Mock).mockReturnValue({
      companyId: 1234,
    });

    (useGetTradeAccreditations as jest.Mock).mockReturnValue({
      tradeAccreditations: mockAccreditations,
      tradeAccreditationsLoading: false,
      error: null,
    });

    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
    cleanup();
  });

  it('should pass correct props to AccreditationList', () => {
    renderTradeAccreditations();

    expect(useGetTradeAccreditations).toHaveBeenCalledWith({
      personId: '99999',
      companyId: 1234,
    });
  });

  it('should navigate to create screen when add button is pressed', () => {
    renderTradeAccreditations();

    expect(mockNavigate).toHaveBeenCalledWith(
      MY_TEAM_TRADE_ACCREDITATIONS_CREATE_SCREEN,
      {
        personId: '99999',
      },
    );
  });

  it('should navigate to update screen when an item is pressed', () => {
    renderTradeAccreditations();

    // AccreditationList mock automatycznie wywołuje onItemPress z id=123
    expect(mockNavigate).toHaveBeenCalledWith(
      MY_TEAM_TRADE_ACCREDITATIONS_UPDATE_SCREEN,
      {
        personId: '99999',
        accreditationId: 123,
      },
    );
  });

  it('should render error screen when there is an error', () => {
    (useGetTradeAccreditations as jest.Mock).mockReturnValue({
      tradeAccreditations: [],
      tradeAccreditationsLoading: false,
      error: new Error('Test error'),
    });

    jest.mock('src/screens/ErrorScreen', () => ({
      ErrorScreen: () => <div data-testid="error-screen" />,
    }));

    const { getByTestId } = renderTradeAccreditations();

    expect(getByTestId).not.toBeNull();
  });

  it('should render error screen when company id is not available', () => {
    (useUserContext as jest.Mock).mockReturnValue({
      companyId: undefined,
    });

    jest.mock('src/screens/ErrorScreen', () => ({
      ErrorScreen: () => <div data-testid="error-screen" />,
    }));

    const { getByTestId } = renderTradeAccreditations();

    expect(getByTestId).not.toBeNull();
  });
});

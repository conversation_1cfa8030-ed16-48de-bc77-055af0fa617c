import React from 'react';
import {
  ACCREDITATIONS_UPDATE_SCREEN,
  MY_TEAM_TRADE_ACCREDITATIONS_SCREEN,
} from 'src/constants';
import { CompanyAccreditationType } from 'src/data/schemas/firestore';
import { RouteProp, useFocusEffect, useRoute } from '@react-navigation/native';
import { AllScreensParamList } from 'src/navigation/routes';
import { useCompanyContext } from 'src/hooks/useCompany';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { StripModifiedDate } from '../Accreditations/utilities';
import { useCreateAccreditation } from './hooks/useCreateAccreditation';
import { ErrorScreen } from '../ErrorScreen';
import { CreateAccreditation } from '../Accreditations/shared/CreateAccreditation';

export const TradeAccreditationsCreate: React.FC = () => {
  const { onCreateAccreditation, deletedMap, existingIds } =
    useCreateAccreditation();
  const { company } = useCompanyContext();

  const route =
    useRoute<
      RouteProp<AllScreensParamList, typeof MY_TEAM_TRADE_ACCREDITATIONS_SCREEN>
    >();
  const { personId } = route.params ?? {};
  if (!personId) {
    throw new InvalidNavParamsError();
  }

  const mappedProofs = (proof: CompanyAccreditationType['proof']) => {
    return proof
      ? proof.map((proofItem) => ({
          ...proofItem,
          uploadedDate: proofItem.uploadedDate?.toISOString(),
        }))
      : undefined;
  };

  useFocusEffect(() => {
    logEvent(EVENT_TYPE.MY_TEAM_NEW_ACCREDITATION_SCREEN_VIEWED);
  });

  if (!company?.id) {
    return <ErrorScreen />;
  }

  const handleCreateAccreditation = async (
    accreditation: StripModifiedDate<CompanyAccreditationType>,
  ) => {
    const options = {
      accreditationId: accreditation.accreditationId,
      expiryDate: accreditation.expiryDate?.toISOString(),
      proof: mappedProofs(accreditation.proof),
      personId,
      companyId: company.id,
    };

    return onCreateAccreditation(options);
  };

  return (
    <CreateAccreditation
      deletedMap={deletedMap}
      existingIds={existingIds}
      routeToUpdate={ACCREDITATIONS_UPDATE_SCREEN}
      onSubmit={handleCreateAccreditation}
    />
  );
};

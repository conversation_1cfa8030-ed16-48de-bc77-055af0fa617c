import { useNavigation } from '@react-navigation/native';
import React, { useEffect } from 'react';
import { ScrollView } from 'react-native';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { Typography } from '@cat-home-experts/react-native-components';
import { IS_WEB, MY_TEAM_VIEW_EMPLOYEE_SCREEN } from 'src/constants';
import { ContentSegment } from 'src/components/ContentSegment/ContentSegment';
import { useEmployeeRoute } from './hooks/useEmployeeRoute';
import { AccreditationNeedHelp } from '../Accreditations/components/AccreditationNeedHelp';
import { styles as utilsStyles } from './utilities/utils';

export const ViewEmployee: React.FC = () => {
  const navigation = useNavigation();
  const route = useEmployeeRoute<typeof MY_TEAM_VIEW_EMPLOYEE_SCREEN>();
  const { employeeName } = route.params ?? {};

  useEffect(() => {
    if (employeeName) {
      navigation.setOptions({
        title: employeeName,
      });
    }
  }, [navigation, employeeName]);

  return (
    <ScrollView style={[styles.container, utilsStyles.lightBlueBackground]}>
      <ContentSegment>
        <Typography>{'View Employee Screen'}</Typography>
      </ContentSegment>
      <ContentSegment style={styles.segment}>
        <AccreditationNeedHelp
          headerStyle={styles.needHelpHeader}
          descriptionStyle={styles.needHelpDescription}
        />
      </ContentSegment>
    </ScrollView>
  );
};

const styles = createMortarStyles(({ spacing }) => ({
  container: {
    padding: IS_WEB ? spacing(3) : 0,
    paddingVertical: IS_WEB ? spacing(3) : spacing(2),
  },
  segment: {
    borderTopWidth: 0,
    borderBottomWidth: 0,
    borderLeftWidth: 0,
    borderRightWidth: 0,
  },
  needHelpHeader: {
    fontSize: 18,
  },
  needHelpDescription: {
    fontSize: 12,
  },
}));

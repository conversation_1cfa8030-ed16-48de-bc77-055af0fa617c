import React from 'react';
import { MY_TEAM_TRADE_ACCREDITATIONS_UPDATE_SCREEN } from 'src/constants';
import { useUserContext } from 'src/hooks/useUser';
import { CompanyAccreditationType } from 'src/data/schemas/firestore';
import { AccreditationResponseType } from 'src/data/schemas/api/trade-app-bff/person/PersonAccreditationResponse';
import { AccreditationStatusType } from 'src/data/schemas/firestore/accreditations/common';
import { Loader } from '@cat-home-experts/react-native-components';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { UpdateAccreditation } from '../Accreditations/shared';
import { useMyTeamRoute } from './hooks/useMyTeamRoute';
import { useUpdateAccreditation } from './hooks/useUpdateAccreditation';
import { ACCREDITATION_DELETE_SUBTITLE } from './constants';
import { ErrorScreen } from '../ErrorScreen';
import { AccreditationStatus } from '../Accreditations/constants';

export const TradeAccreditationsUpdate: React.FC = () => {
  const { companyId } = useUserContext();
  const route =
    useMyTeamRoute<typeof MY_TEAM_TRADE_ACCREDITATIONS_UPDATE_SCREEN>();
  const { accreditationId, personId } = route.params ?? {};
  if (!accreditationId || !personId) {
    throw new InvalidNavParamsError();
  }

  const {
    data: accreditation,
    isLoading,
    isError,
    onUpdateAccreditation,
    onDeleteAccreditation,
  } = useUpdateAccreditation({ personId, companyId, accreditationId });

  if (isError || !companyId || !accreditationId || !personId) {
    return <ErrorScreen />;
  }

  if (isLoading) {
    return <Loader />;
  }

  const mappedProofs = (proof: CompanyAccreditationType['proof']) => {
    return proof
      ? proof.map((proofItem) => ({
          ...proofItem,
          uploadedDate: proofItem.uploadedDate?.toISOString(),
        }))
      : undefined;
  };

  const reverseMappedProofs = (proof: AccreditationResponseType['proof']) => {
    return proof
      ? proof.map((proofItem) => ({
          ...proofItem,
          uploadedDate: new Date(proofItem.uploadedDate),
        }))
      : [];
  };

  const mapAccreditationStatus = (
    status: AccreditationStatus | undefined,
  ): AccreditationStatusType => {
    if (!status) {
      return AccreditationStatusType.Pending;
    }

    // Map between the two enum types
    const statusMap: Record<AccreditationStatus, AccreditationStatusType> = {
      [AccreditationStatus.Active]: AccreditationStatusType.Approved,
      [AccreditationStatus.Expired]: AccreditationStatusType.Rejected,
      [AccreditationStatus.ExpiresSoon]: AccreditationStatusType.Approved, // Assuming ExpiresSoon is still valid
      [AccreditationStatus.PendingReview]: AccreditationStatusType.Pending,
      [AccreditationStatus.Rejected]: AccreditationStatusType.Rejected,
      [AccreditationStatus.FurtherActionRequired]:
        AccreditationStatusType.Pending,
    };

    return statusMap[status] || AccreditationStatusType.Pending;
  };

  const handleUpdateAccreditation = (
    newAccreditation: CompanyAccreditationType,
  ) => {
    const options = {
      accreditationId: newAccreditation.accreditationId,
      expiryDate: newAccreditation.expiryDate?.toISOString(),
      proof: mappedProofs(newAccreditation.proof),
      personId,
      companyId,
    };

    return onUpdateAccreditation(options);
  };

  const handleDeleteAccreditation = () => {
    logEvent(EVENT_TYPE.MY_TEAM_ACCREDITATION_DELETED);
    return onDeleteAccreditation({ personId, companyId, accreditationId });
  };

  return (
    <UpdateAccreditation
      deleteModalSubtitle={ACCREDITATION_DELETE_SUBTITLE}
      companyAccreditation={
        accreditation
          ? {
              ...accreditation,
              proof: reverseMappedProofs(accreditation.proof || []),
              isDeleted: false,
              history: [],
              modifiedDate: accreditation.statusDate,
              status: mapAccreditationStatus(accreditation.status),
            }
          : undefined
      }
      onUpdate={handleUpdateAccreditation}
      onDelete={handleDeleteAccreditation}
    />
  );
};

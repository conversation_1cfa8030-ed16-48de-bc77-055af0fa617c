import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';
import { Alert } from 'react-native';
import { MY_TEAM_EDIT_EMPLOYEE_SCREEN, MY_TEAM_SCREEN } from 'src/constants';
import {
  type AddTeamPersonRequestType,
  ContractingType,
  updateTeamPersonRequestSchema,
  UpdateTeamPersonRequestType,
} from 'src/data/schemas/api/trade-app-bff/team';
import { useUserContext } from 'src/hooks/useUser';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { useEmployeeRoute } from '../../hooks/useEmployeeRoute';
import { useTeamPerson } from '../../hooks/useTeamPerson';
import { EmployeeForm } from '../Common';

export const EditEmployee: React.FC = () => {
  const { companyId } = useUserContext();
  const route = useEmployeeRoute<typeof MY_TEAM_EDIT_EMPLOYEE_SCREEN>();
  const { person } = route.params ?? {};
  if (!person) {
    throw new InvalidNavParamsError();
  }

  const methods = useForm({
    mode: 'onChange',
    resolver: zodResolver(updateTeamPersonRequestSchema),
    defaultValues: {
      ...person,
      companyId,
      contractingType: ContractingType.Employee,
      phoneNumber: person.phone,
      dateOfBirth: person.dateOfBirth
        ? new Date(person.dateOfBirth)
        : undefined,
    },
  });
  const navigation = useNavigation();

  const { updateTeamPerson } = useTeamPerson(person.id);

  const onValidFormSubmit = async (data: UpdateTeamPersonRequestType) => {
    try {
      if (companyId) {
        data.companyId = companyId;
        await updateTeamPerson(data);
        navigation.navigate(MY_TEAM_SCREEN);
      }
    } catch (error) {
      // TODO: Generic Error Modal - https://checkatrade.atlassian.net/browse/DSU-3236
      Alert.alert('Error', 'Error editing person');
      console.error('Error editing person:', error);
    }
  };

  return (
    <EmployeeForm
      person={person}
      form={
        methods as UseFormReturn<
          AddTeamPersonRequestType,
          unknown,
          AddTeamPersonRequestType
        >
      }
      onValidFormSubmit={onValidFormSubmit}
    />
  );
};

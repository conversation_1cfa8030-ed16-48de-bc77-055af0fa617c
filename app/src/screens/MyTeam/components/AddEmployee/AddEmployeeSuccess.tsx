import {
  Button,
  Icon,
  Typography,
} from '@cat-home-experts/react-native-components';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { useNavigation } from '@react-navigation/native';
import React, { useEffect } from 'react';
import { ScrollView, View } from 'react-native';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import {
  MY_TEAM_ADD_EMPLOYEE_SCREEN,
  MY_TEAM_ADD_EMPLOYEE_SUCCESS_SCREEN,
  MY_TEAM_SCREEN,
} from 'src/constants';
import { useMobileMediaQuery } from 'src/hooks/useMediaQuery';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { useEmployeeRoute } from '../../hooks/useEmployeeRoute';
import { AddEmployeeSuccessStepList } from './AddEmployeeSuccessStepList';

export const AddEmployeeSuccess: React.FC = () => {
  const isMobile = useMobileMediaQuery();
  const navigation = useNavigation();
  const route = useEmployeeRoute<typeof MY_TEAM_ADD_EMPLOYEE_SUCCESS_SCREEN>();
  const { personId } = route.params ?? {};
  if (!personId) {
    throw new InvalidNavParamsError();
  }

  const onPressAddAnotherEmployee = () => {
    navigation.navigate(MY_TEAM_ADD_EMPLOYEE_SCREEN);
  };

  const onPressDone = () => {
    navigation.navigate(MY_TEAM_SCREEN);
  };

  useEffect(() => {
    logEvent(EVENT_TYPE.OSS_ADD_SUCCESS);
  }, []);

  return (
    <ScrollView style={styles.container}>
      <View style={[styles.wrapper, !isMobile && styles.marginWeb]}>
        <View style={styles.iconContainer}>
          <Icon
            name="check-circle-fill"
            size={40}
            color="#23A26D"
            style={styles.icon}
          />
        </View>
        <Typography
          useVariant="headingSMSemiBold"
          isCentred
          style={styles.title}
        >
          {'Onsite staff added successfully!'}
        </Typography>

        <View style={styles.listContainer}>
          <Typography useVariant="bodyMedium" style={styles.listTitle}>
            {'What happens next?'}
          </Typography>
          <AddEmployeeSuccessStepList personId={personId} />
        </View>

        <View style={styles.buttonContainer}>
          <Button
            label="Add another onsite staff"
            variant="secondary"
            onPress={onPressAddAnotherEmployee}
            block
          />
          <Button
            label="Done"
            variant="outlineSecondary"
            onPress={onPressDone}
            block
          />
        </View>
      </View>
    </ScrollView>
  );
};

const styles = createMortarStyles(({ spacing, palette }) => ({
  container: {
    flex: 1,
    width: '100%',
  },
  wrapper: {
    backgroundColor: palette.mortarV3.tokenNeutral0,
    padding: spacing(3),
  },
  marginWeb: { margin: spacing(3) },
  title: {
    marginBottom: spacing(3),
  },
  icon: {
    alignSelf: 'center',
  },
  iconContainer: {
    alignSelf: 'center',
    backgroundColor: '#E5F4ED',
    borderRadius: 40,
    padding: spacing(2),
    marginBottom: spacing(2),
  },
  buttonContainer: {
    gap: spacing(1),
    marginVertical: spacing(3.5),
  },
  listContainer: {
    backgroundColor: palette.mortarV3.tokenDefault100,
    borderRadius: spacing(1),
    padding: spacing(2),
  },
  listTitle: {
    marginBottom: spacing(2),
  },
}));

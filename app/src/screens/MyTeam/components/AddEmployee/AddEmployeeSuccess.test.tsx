import { useNavigation, useRoute } from '@react-navigation/native';
import { cleanup, fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import { MY_TEAM_ADD_EMPLOYEE_SCREEN, MY_TEAM_SCREEN } from 'src/constants';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { AddEmployeeSuccess } from './AddEmployeeSuccess';

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
  useRoute: jest.fn(),
}));

jest.mock('src/services/analytics', () => ({
  logEvent: jest.fn(),
}));

describe('Screens | MyTeam | components | AddEmployee |AddEmployeeSuccess', () => {
  const mockNavigate = jest.fn();

  beforeEach(() => {
    (useNavigation as jest.Mock).mockReturnValue({
      navigate: mockNavigate,
    });
    (useRoute as jest.Mock).mockReturnValue({
      params: {
        personId: 'person123',
      },
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
    cleanup();
  });

  it('renders component correctly', () => {
    const { getByText } = render(<AddEmployeeSuccess />);

    expect(getByText('Onsite staff added successfully!')).toBeDefined();
    expect(getByText('What happens next?')).toBeDefined();
    expect(getByText('Add another onsite staff')).toBeDefined();
    expect(getByText('Done')).toBeDefined();

    // Check if steps are displayed
    expect(getByText(/Your onsite staff has been notified/)).toBeOnTheScreen();

    expect(logEvent).toHaveBeenCalledWith(EVENT_TYPE.OSS_ADD_SUCCESS);
  });

  it('navigates to add employee screen when "Add another onsite staff" is clicked', () => {
    const { getByText } = render(<AddEmployeeSuccess />);

    const addAnotherButton = getByText('Add another onsite staff');
    fireEvent.press(addAnotherButton);

    expect(mockNavigate).toHaveBeenCalledWith(MY_TEAM_ADD_EMPLOYEE_SCREEN);
  });

  it('navigates to team screen when "Done" is clicked', () => {
    const { getByText } = render(<AddEmployeeSuccess />);

    const doneButton = getByText('Done');
    fireEvent.press(doneButton);

    expect(mockNavigate).toHaveBeenCalledWith(MY_TEAM_SCREEN);
  });
});

import React from 'react';
import { AccreditationList } from 'src/screens/Accreditations/shared';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import {
  MY_TEAM_TRADE_ACCREDITATIONS_CREATE_SCREEN,
  MY_TEAM_TRADE_ACCREDITATIONS_SCREEN,
  MY_TEAM_TRADE_ACCREDITATIONS_UPDATE_SCREEN,
} from 'src/constants';
import { useUserContext } from 'src/hooks/useUser';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { useGetTradeAccreditations } from './hooks/useGetTradeAccreditations';
import { useMyTeamRoute } from './hooks/useMyTeamRoute';
import { ErrorScreen } from '../ErrorScreen';

export const TradeAccreditations: React.FC = () => {
  const { companyId } = useUserContext();

  const navigation = useNavigation();
  const route = useMyTeamRoute<typeof MY_TEAM_TRADE_ACCREDITATIONS_SCREEN>();
  const { personId } = route.params ?? {};
  if (!personId) {
    throw new InvalidNavParamsError();
  }

  const { tradeAccreditations, tradeAccreditationsLoading, error } =
    useGetTradeAccreditations({ personId, companyId });

  const onAddButtonPress = () => {
    navigation.navigate(MY_TEAM_TRADE_ACCREDITATIONS_CREATE_SCREEN, {
      personId,
    });
  };

  const onItemPress = (accreditationId: number) => {
    navigation.navigate(MY_TEAM_TRADE_ACCREDITATIONS_UPDATE_SCREEN, {
      personId,
      accreditationId: accreditationId,
    });
  };

  useFocusEffect(() => {
    logEvent(EVENT_TYPE.MY_TEAM_TRADE_ACCREDITATIONS_SCREEN_VIEWED);
  });

  if (!companyId || error) {
    return <ErrorScreen />;
  }

  return (
    <AccreditationList
      companyAccreditations={tradeAccreditations}
      companyAccreditationsLoading={tradeAccreditationsLoading}
      onAddButtonPress={onAddButtonPress}
      onItemPress={onItemPress}
      isCompanyAccreditation={false}
    />
  );
};

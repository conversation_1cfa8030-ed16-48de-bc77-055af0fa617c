import React, { ReactElement, useEffect, useMemo, useState } from 'react';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import {
  KeyboardAvoidingView,
  ScrollView,
  StyleSheet,
  TextInput,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { addDays, formatISO, parseISO } from 'date-fns';
import { Button } from '@cat-home-experts/react-native-components';
import {
  tokenColorBlack,
  tokenColorLightBlue,
  tokenColorPrimaryWhite,
  tokenColorSystemOrange,
} from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';

import {
  IS_IOS,
  IS_WEB,
  QUOTES_AND_INVOICES_INVOICE_DETAILS_SCREEN,
  SAVED_INVOICE_SCREEN,
} from 'src/constants';
import { Overlay } from 'src/components/primitives/Overlay';
import { createTestIds } from 'src/utilities/testIds';
import {
  Loader,
  NavBackIcon,
  SimpleDatePicker,
  showToast,
} from 'src/components';
import { TextWithIcon } from 'src/screens/QuotesAndInvoices/components/TextWithIcon';
import { CustomerAddress } from 'src/screens/QuotesAndInvoices/CustomerAddress';
import { LineItemList } from 'src/screens/QuotesAndInvoices/LineItemList/LineItemList';
import { CostsSummary } from 'src/screens/QuotesAndInvoices/CostsSummary';
import { Invoice } from 'src/screens/QuotesAndInvoices/quotes.types';
import { useQuoteDetails } from 'src/screens/QuotesAndInvoices/QuoteDetails/useQuoteDetails';
import { VatRegNumber } from 'src/screens/QuotesAndInvoices/components/VatRegNumber/VatRegNumber';
import {
  CONVERT_TO_INVOICE_MODAL,
  INVOICE_DETAILS_FORM,
  REVERT_TO_QUOTE_MODAL,
} from 'src/screens/QuotesAndInvoices/InvoiceDetails/constants';
import { InformationModal } from 'src/components/InformationModal';
import { isConvertToInvoiceFormDirty } from 'src/screens/QuotesAndInvoices/utils';
import type { AllScreensParamList } from 'src/navigation/routes';
import { EVENT_TYPE } from 'src/constants.events';

const TEST_IDS = createTestIds('invoice-details', {
  CROSS_NAV_BUTTON: 'cross-nav-button',
  INVOICE_NUMBER_INPUT: 'invoice-number-input',
  INVOICE_CREATED_DATE_PICKER: 'invoice-date-picker',
  INVOICE_DUE_DATE_PICKER: 'due-date-picker',
  SAVE_BUTTON: 'save-button',
});

const currentDate = new Date();
const weekFromNowDate = addDays(currentDate, 7);

export function InvoiceDetails(): ReactElement {
  const navigation = useNavigation();
  const route =
    useRoute<
      RouteProp<
        AllScreensParamList,
        typeof QUOTES_AND_INVOICES_INVOICE_DETAILS_SCREEN
      >
    >();
  const { quoteId, screenType } = route.params ?? {};

  // form
  const [formState, setFormState] = useState<Invoice>();

  // modals
  const [isRevertToQuoteModalVisible, setIsRevertToQuoteModalVisible] =
    useState(false);
  const [isConvertToInvoiceModalVisible, setIsConvertToInvoiceModalVisible] =
    useState(false);

  const { data, convertToInvoice } = useQuoteDetails(quoteId);

  const isSaveAvailable = useMemo(() => {
    return formState?.referenceNumber;
  }, [formState]);

  useEffect(() => {
    if (data) {
      setFormState({
        ...data,
        invoiceCreatedDate: currentDate,
        invoiceDueDate: weekFromNowDate,
      });
    }
  }, [data]);

  if (!formState) {
    return <Loader />;
  }

  const handleReferenceNumberChange = (value: string) => {
    setFormState({ ...formState, referenceNumber: value });
  };

  const handleInvoiceCreatedDatePick = (createdDate: Date) => {
    const parsedDate = parseISO(formatISO(createdDate));
    setFormState({ ...formState, invoiceCreatedDate: parsedDate });
  };

  const handleInvoiceDueDatePick = (dueDate: Date) => {
    const parsedDate = parseISO(formatISO(dueDate));
    setFormState({ ...formState, invoiceDueDate: parsedDate });
  };

  const handleBackNav = () => {
    if (
      isConvertToInvoiceFormDirty(
        formState,
        currentDate,
        weekFromNowDate,
        data?.referenceNumber,
      )
    ) {
      setIsRevertToQuoteModalVisible(true);
    } else {
      navigation.goBack();
    }
  };

  const handleSave = () => {
    setIsConvertToInvoiceModalVisible(true);
  };

  const handleRevert = () => {
    setIsRevertToQuoteModalVisible(false);
    navigation.goBack();
  };

  const handleConvert = async () => {
    setIsRevertToQuoteModalVisible(false);

    try {
      await convertToInvoice(formState);

      if (data) {
        navigation.navigate(SAVED_INVOICE_SCREEN, {
          invoiceId: data.quoteId,
          screenType,
        });
      }
    } catch (error) {
      showToast({
        text1: INVOICE_DETAILS_FORM.TOAST_ERROR,
        type: 'error',
      });
    } finally {
      setIsConvertToInvoiceModalVisible(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={IS_IOS ? 'height' : undefined}
      style={styles.keyboardAvoidContainer}
    >
      <SafeAreaView edges={['top', 'left', 'right']} style={styles.container}>
        <View style={styles.headerIconWrapper}>
          <NavBackIcon
            testID={TEST_IDS.CROSS_NAV_BUTTON}
            color="black"
            iconName="cross"
            onPress={handleBackNav}
            style={styles.iconStyle}
          />
        </View>
        <ScrollView style={styles.root} testID={TEST_IDS.ROOT}>
          <View style={styles.content}>
            <View style={styles.titleContainer}>
              <TextInput
                editable={false}
                style={styles.titleText}
                value={formState?.title}
              />
            </View>

            <TextWithIcon
              sectionHeader={INVOICE_DETAILS_FORM.INVOICE_NUMBER}
              testID={TEST_IDS.INVOICE_NUMBER_INPUT}
              showIcon={formState.referenceNumber === ''}
              iconName="hash"
              placeholderText={INVOICE_DETAILS_FORM.ADD_INVOICE_NUMBER}
              value={formState.referenceNumber}
              onChange={handleReferenceNumberChange}
              editable={true}
            />
            <SimpleDatePicker
              sectionHeader={INVOICE_DETAILS_FORM.INVOICE_DATE}
              shownDate={formState.invoiceCreatedDate || currentDate}
              onConfirm={handleInvoiceCreatedDatePick}
              testID={TEST_IDS.INVOICE_CREATED_DATE_PICKER}
              containerStyle={styles.invoiceCreatedDatePickerContainer}
            />
            <SimpleDatePicker
              sectionHeader={INVOICE_DETAILS_FORM.DUE_DATE}
              shownDate={formState.invoiceDueDate || weekFromNowDate}
              onConfirm={handleInvoiceDueDatePick}
              testID={TEST_IDS.INVOICE_DUE_DATE_PICKER}
              containerStyle={styles.invoiceDueDatePickerContainer}
            />
            <Overlay
              wrapperStyles={styles.overlayStyles}
              opacity={0.65}
              isEnabled={true}
            >
              <>
                <CustomerAddress
                  customerAddress={formState.homeownerDetails}
                  hasFullAddress={true}
                />
                <LineItemList
                  lineItems={formState.lineItems}
                  editable={false}
                />
                <CostsSummary
                  discountAmount={formState.discountAmount}
                  subtotalAmount={formState.subtotalAmount}
                  totalAmount={formState.totalAmount}
                  vatAmount={formState.vatAmount}
                />
                <VatRegNumber
                  vatRegNumber={formState.vatRegistrationNumber}
                  isVisible={formState.vatAmount > 0}
                  hasValidVATRegNumber={true}
                  editable={false}
                />
                <TextWithIcon
                  sectionHeader={INVOICE_DETAILS_FORM.TERMS_AND_CONDITIONS}
                  showIcon={false}
                  iconName="hash"
                  value={formState.terms}
                  editable={false}
                />
              </>
            </Overlay>
            <Button
              label={INVOICE_DETAILS_FORM.SAVE}
              onPress={handleSave}
              variant="secondary"
              block
              isDisabled={!isSaveAvailable || formState.converted}
              style={styles.button}
              testID={TEST_IDS.SAVE_BUTTON}
            />
          </View>
        </ScrollView>

        {isRevertToQuoteModalVisible && (
          <InformationModal
            onPressPrimaryButton={handleRevert}
            onPressMutedButton={() => setIsRevertToQuoteModalVisible(false)}
            icon="warning-circle-fill"
            iconColor={tokenColorSystemOrange}
            modalTitle={REVERT_TO_QUOTE_MODAL.TITLE}
            modalDescription={REVERT_TO_QUOTE_MODAL.DESCRIPTION}
            primaryButtonLabel={REVERT_TO_QUOTE_MODAL.PRIMARY_BUTTON_LABEL}
            mutedButtonLabel={REVERT_TO_QUOTE_MODAL.MUTED_BUTTON_LABEL}
            eventName={EVENT_TYPE.QUOTE_INVOICE_REVERT_TO_QUOTE}
          />
        )}

        {isConvertToInvoiceModalVisible && (
          <InformationModal
            onPressPrimaryButton={handleConvert}
            onPressMutedButton={() => setIsConvertToInvoiceModalVisible(false)}
            icon="warning-circle-fill"
            iconColor={tokenColorSystemOrange}
            modalTitle={CONVERT_TO_INVOICE_MODAL.TITLE}
            modalDescription={CONVERT_TO_INVOICE_MODAL.DESCRIPTION}
            primaryButtonLabel={CONVERT_TO_INVOICE_MODAL.PRIMARY_BUTTON_LABEL}
            mutedButtonLabel={CONVERT_TO_INVOICE_MODAL.MUTED_BUTTON_LABEL}
            eventName={EVENT_TYPE.QUOTE_CONVERT_TO_INVOICE_MODAL}
          />
        )}
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
}

InvoiceDetails.testIds = TEST_IDS;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: tokenColorPrimaryWhite,
  },
  root: {
    width: '100%',
  },
  keyboardAvoidContainer: {
    backgroundColor: tokenColorLightBlue,
    justifyContent: 'center',
    flexDirection: 'column',
    flex: 1,
  },
  content: {
    width: '100%',
    maxWidth: 800,
    alignSelf: 'center',
    backgroundColor: tokenColorLightBlue,
  },
  titleContainer: {
    backgroundColor: tokenColorPrimaryWhite,
    flexDirection: 'row',
    paddingHorizontal: 24,
    paddingVertical: 14,
    alignItems: 'center',
    marginRight: 10,
  },
  titleText: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: 'semi-bold',
    padding: 0,
    width: IS_WEB ? '100%' : undefined,
    color: tokenColorBlack,
  },
  button: {
    marginTop: 16,
    marginBottom: 32,
    marginHorizontal: 24,
  },
  iconStyle: {
    backgroundColor: tokenColorPrimaryWhite,
    paddingVertical: 16,
    paddingHorizontal: 28,
    width: 80,
  },
  headerIconWrapper: {
    backgroundColor: tokenColorPrimaryWhite,
  },
  overlayStyles: {
    backgroundColor: tokenColorLightBlue,
  },
  invoiceCreatedDatePickerContainer: {
    zIndex: 20,
  },
  invoiceDueDatePickerContainer: {
    zIndex: 15,
  },
});

import React from 'react';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { logEvent } from 'src/services/analytics';
import { ANALYTICS_ACTION_TYPE, EVENT_TYPE } from 'src/constants.events';
import { View } from 'react-native';
import {
  JOB_PAYMENTS_QUOTE_NEW_PAYMENT_SCREEN,
  QUOTE_PAYMENTS_LIST_SCREEN,
} from 'src/constants';
import { TextButtonWithIcon } from 'src/components/TextButtonWithIcon/TextButtonWithIcon';
import { QuotePaymentsList } from 'src/screens/QuotesAndInvoices/Payments/QuotePaymentsList';
import {
  createMortarStyles,
  createTestIds,
  spacing,
} from '@cat-home-experts/react-native-utilities';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { AllScreensParamList } from 'src/navigation/routes';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { REQUEST_PAYMENT_BUTTON } from './constants';

const TEST_IDS = createTestIds('quote-payments', {});

export function QuotePayments(): ReturnType<React.FC> {
  const { bottom } = useSafeAreaInsets();
  const navigation = useNavigation();
  const route =
    useRoute<
      RouteProp<AllScreensParamList, typeof QUOTE_PAYMENTS_LIST_SCREEN>
    >();
  const { jobId, quoteId, quotePenceTotal } = route.params ?? {};
  if (!quoteId) {
    throw new InvalidNavParamsError();
  }

  const handleNewCreatePaymentPress = () => {
    logEvent(
      `${EVENT_TYPE.QUOTE_PAYMENTS_LIST_NEW_PAYMENT}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
    );
    navigation.navigate(JOB_PAYMENTS_QUOTE_NEW_PAYMENT_SCREEN, {
      quoteId,
      jobId,
      quotePenceTotal,
    });
  };

  return (
    <View
      style={[styles.screen, { paddingBottom: bottom }]}
      testID={TEST_IDS.ROOT}
    >
      <TextButtonWithIcon
        title={REQUEST_PAYMENT_BUTTON.title}
        mortarIcon={REQUEST_PAYMENT_BUTTON.icon}
        isNavigatable={true}
        onPress={handleNewCreatePaymentPress}
      />
      <View style={styles.listContainer}>
        <QuotePaymentsList quoteId={quoteId} />
      </View>
    </View>
  );
}

const styles = createMortarStyles(({ palette }) => ({
  screen: { flex: 1, backgroundColor: palette.mortarV3.tokenNeutral0 },
  listContainer: {
    paddingHorizontal: spacing(2),
  },
}));

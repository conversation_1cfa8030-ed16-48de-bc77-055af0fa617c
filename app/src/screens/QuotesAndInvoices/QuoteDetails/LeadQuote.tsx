import React, { ReactElement, useEffect, useMemo, useState } from 'react';
import { RouteProp, useRoute } from '@react-navigation/native';
import { isTruthy } from '@cat-home-experts/react-native-utilities';
import { LEAD_QUOTE_SCREEN } from 'src/constants';
import { useArchivedJobDetails } from 'src/screens/Jobs/hooks/useArchivedJobDetails';
import { useMarketplaceJobDetails } from 'src/screens/MarketplaceJobs/hooks/useMarketplaceJobDetails';
import {
  getJobConsumerName,
  getJobTitle,
} from 'src/screens/MarketplaceJobs/utilities';
import { AllScreensParamList } from 'src/navigation/routes';
import { Loader } from 'src/components';
import { useChannelSmartMessages } from 'src/services/stream/hooks/useChannelSmartMessages';
import { useJobRelatedChannel } from 'src/services/stream/hooks/useJobRelatedChannel';
import {
  SharedAddressSmartMessage,
  SmartMessage,
  SmartMessageType,
} from 'src/services/stream/smartMessages';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { DEFAULT_NEW_QUOTE, DEFAULT_TITLE } from './constants';
import { QuoteDetails } from './QuoteDetails';
import { Quote } from '../quotes.types';
import { useQuoteDetails } from './useQuoteDetails';

interface StandardisedJobData {
  id: string;
  title: string;
  consumerName: string;
  address: {
    address: string;
    addressLine2: string;
    city: string;
    postcode: string;
  };
  email?: string;
  phone?: string;
  isMarketplaceJob: boolean;
}

const isSharedAddressMessage = (
  msg?: SmartMessage | SharedAddressSmartMessage | null | string,
): msg is SharedAddressSmartMessage =>
  typeof msg !== 'string' &&
  (msg?.smartType === SmartMessageType.ADDRESS_SHARED ||
    msg?.smartType === SmartMessageType.ADDRESS_REQUESTED);

export function LeadQuote(): ReactElement {
  const route =
    useRoute<RouteProp<AllScreensParamList, typeof LEAD_QUOTE_SCREEN>>();
  const { jobId, isArchivedJob, consumerId } = route.params ?? {};
  if (!jobId) {
    throw new InvalidNavParamsError();
  }

  const { archivedJob } = useArchivedJobDetails(jobId, isArchivedJob);
  const { job } = useMarketplaceJobDetails(jobId, !isArchivedJob);

  const { channel, isLoading: isChannelLoading } = useJobRelatedChannel(
    isArchivedJob ? undefined : jobId,
  );
  const smartMessages = useChannelSmartMessages({
    channel,
    fetch: true,
  });

  const { createQuote } = useQuoteDetails();

  const [initialFormState, setInitialFormState] = useState<Quote | null>(null);

  const jobData: StandardisedJobData = useMemo(() => {
    const noChannelForJob = !channel && !isChannelLoading;

    if (isTruthy(job?.id) && (isTruthy(smartMessages) || noChannelForJob)) {
      const addressSmartMessage =
        smartMessages?.[SmartMessageType.ADDRESS_SHARED] ??
        smartMessages?.[SmartMessageType.ADDRESS_REQUESTED];

      return {
        id: job.id,
        title: getJobTitle(job),
        consumerName: getJobConsumerName(job),
        address: {
          address: isSharedAddressMessage(addressSmartMessage)
            ? addressSmartMessage?.address?.line1
            : '',
          addressLine2: isSharedAddressMessage(addressSmartMessage)
            ? addressSmartMessage?.address?.line2
            : '',
          city: job.address?.city || '',
          postcode: job.address?.postcode || '',
        },
        email: job.consumer?.email ?? '',
        phone: job.consumer?.phone ?? '',
        isMarketplaceJob: true,
      };
    }

    if (isTruthy(archivedJob)) {
      return {
        id: archivedJob.jobId,
        title: archivedJob.title || '',
        consumerName: archivedJob.consumerName || '',
        address: {
          address: '',
          addressLine2: '',
          postcode: archivedJob.postcode || '',
          city: '',
        },
        email: archivedJob.consumerDetails?.email || '',
        phone: archivedJob.consumerDetails?.phone || '',
        isMarketplaceJob: false,
      };
    }

    return {} as StandardisedJobData;
  }, [archivedJob, job, smartMessages, channel, isChannelLoading]);

  useEffect(() => {
    if (jobData.id) {
      const {
        id,
        title,
        consumerName,
        address,
        email,
        phone,
        isMarketplaceJob,
      } = jobData;

      const quoteInfo = {
        ...DEFAULT_NEW_QUOTE,
        jobId: id,
        ...(isTruthy(job) && { jobIdV2: id, opportunityId: job.channelId }),
        ...(consumerId && { consumerId: consumerId }),
        title: title || DEFAULT_TITLE,
        isMarketplaceJob: isMarketplaceJob,
        homeownerDetails: {
          name: consumerName || '',
          address: address.address || '',
          addressLine2: address.addressLine2 || '',
          city: address.city || '',
          postcode: address.postcode || '',
          emailAddress: email || '',
          phoneNumber: phone || '',
        },
      };

      if (isTruthy(jobData.id)) {
        setInitialFormState(quoteInfo);
      }
    }
  }, [job, jobData, consumerId]);

  if (initialFormState?.jobId !== jobId) {
    return <Loader />;
  }

  return (
    <QuoteDetails
      initialState={initialFormState}
      createQuote={createQuote}
      showInitialErrors
    />
  );
}

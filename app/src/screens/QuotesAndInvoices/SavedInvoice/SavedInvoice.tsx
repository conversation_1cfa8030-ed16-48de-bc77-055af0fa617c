import React, { ReactElement, useEffect, useState } from 'react';
import { ScrollView } from 'react-native';
import { differenceInSeconds } from 'date-fns';
import {
  RouteProp,
  StackActions,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import * as FileSystem from 'expo-file-system';
import useLatestCallback from 'use-latest-callback';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import type { AllScreensParamList } from 'src/navigation/routes';
import { getDownloadURL } from 'src/services/firebase/storage';
import { HOOK_STATE, IS_WEB, SAVED_INVOICE_SCREEN } from 'src/constants';
import { Loader, NavBackIcon } from 'src/components';
import { PdfPreview } from 'src/screens/QuotesAndInvoices/components/PdfPreview';
import { SavedInvoiceMenu } from 'src/screens/QuotesAndInvoices/SavedInvoice/SavedInvoiceMenu';
import { Invoice } from 'src/screens/QuotesAndInvoices/quotes.types';
import { useQuoteDetails } from 'src/screens/QuotesAndInvoices/QuoteDetails/useQuoteDetails';
import { SCREEN_TYPE } from 'src/screens/QuotesAndInvoices/QuoteDetails/constants';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';

const TEST_IDS = createTestIds('saved-invoice', {});

export function SavedInvoice(): ReactElement {
  const [localFilePath, setLocalFilePath] = useState<string>();
  const [hasFailedPDFFetch, setHasFailedPDFFetch] = useState<boolean>();

  const navigation = useNavigation();
  const isDesktop = useDesktopMediaQuery();
  const route =
    useRoute<RouteProp<AllScreensParamList, typeof SAVED_INVOICE_SCREEN>>();
  const { invoiceId, screenType } = route.params ?? {};
  const { data, hookState, updateDocumentStatus, generatePDF } =
    useQuoteDetails(invoiceId);

  const savedInvoice = data as Invoice; // TODO: refine types to discriminate between Quote / Invoice

  const handleBack = useLatestCallback(() => {
    switch (screenType) {
      case SCREEN_TYPE.Chat:
        navigation.dispatch(StackActions.pop(2));
        break;
      case SCREEN_TYPE.ChatView:
      case SCREEN_TYPE.List:
      default:
        navigation.goBack();
        break;
    }
  });
  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => <NavBackIcon onPress={handleBack} />,
    });
  }, [navigation, handleBack]);

  useEffect(() => {
    if (!data?.pdfReference && data?.lastUpdatedAt) {
      const secondsSinceQuoteCreation = differenceInSeconds(
        new Date(),
        data?.lastUpdatedAt,
      );

      if (secondsSinceQuoteCreation >= 10) {
        setHasFailedPDFFetch(true);
      }
    }
  }, [data?.pdfReference, data?.lastUpdatedAt]);

  useEffect(() => {
    if (!data?.pdfReference) {
      setLocalFilePath(undefined);
      return;
    }

    getDownloadURL(data.pdfReference)
      .then(async (url) => {
        if (IS_WEB) {
          setLocalFilePath(url);
          setHasFailedPDFFetch(false);
          return;
        } else {
          const res = await FileSystem.downloadAsync(
            url,
            FileSystem.documentDirectory + encodeURI('quote_preview.pdf'),
          );
          setLocalFilePath(res.uri);
          setHasFailedPDFFetch(false);
        }
      })
      .catch(() => {
        setHasFailedPDFFetch(true);
      });
  }, [data?.pdfReference]);

  if (
    hookState === HOOK_STATE.LOADING ||
    !savedInvoice ||
    hasFailedPDFFetch === undefined
  ) {
    return <Loader />;
  }

  return (
    <ScrollView
      style={styles.root}
      contentContainerStyle={[
        styles.container,
        isDesktop && styles.desktopContainer,
      ]}
      testID={TEST_IDS.ROOT}
      scrollEnabled={IS_WEB}
    >
      <PdfPreview localFilePath={localFilePath} />

      <SavedInvoiceMenu
        screenType={screenType}
        savedInvoice={savedInvoice}
        updateDocumentStatus={updateDocumentStatus}
        generatePDF={generatePDF}
        hasFailedPDFFetch={hasFailedPDFFetch}
      />
    </ScrollView>
  );
}

SavedInvoice.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  root: {
    flex: 1,
    width: '100%',
  },
  container: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: palette.mortarV3.tokenDefault100,
    width: '100%',
    maxWidth: 1200,
    gap: spacing(2),
  },
  desktopContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    alignSelf: 'center',
    paddingVertical: spacing(2),
    paddingHorizontal: spacing(3),
  },
}));

import React, { ReactElement } from 'react';
import { JobInformation } from 'src/screens/JobDetailsV2/components/JobDetailsContent/JobInformation';
import {
  useMarketplaceJobDetails,
  useMarketplaceJobPropertyFacts,
} from 'src/screens/MarketplaceJobs/hooks';
import { RouteProp, useRoute } from '@react-navigation/native';
import type { AllScreensParamList } from 'src/navigation/routes';
import { Loader } from 'src/components/primitives/Loader';

import { JOB_INFORMATION_SCREEN } from 'src/constants';
import { ErrorScreen } from 'src/screens/ErrorScreen';
import { ScrollView } from 'react-native';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';

export const JobInformationContainer = (): ReactElement => {
  const route =
    useRoute<RouteProp<AllScreensParamList, typeof JOB_INFORMATION_SCREEN>>();
  const { id } = route.params ?? {};
  if (!id) {
    throw new InvalidNavParamsError();
  }

  const { job, isLoading } = useMarketplaceJobDetails(id);
  const { propertyFacts } = useMarketplaceJobPropertyFacts(id);

  if (isLoading) {
    return <Loader />;
  }

  if (!job) {
    return <ErrorScreen title="Job not found" />;
  }

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
    >
      <JobInformation
        job={job}
        hasChatChannel={false}
        propertyFacts={propertyFacts}
        isStandalonePage
      />
    </ScrollView>
  );
};

const styles = createMortarStyles(({ spacing }) => ({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: spacing(4),
  },
}));

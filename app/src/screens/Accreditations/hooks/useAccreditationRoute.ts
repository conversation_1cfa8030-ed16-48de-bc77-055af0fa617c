import { RouteProp, useRoute } from '@react-navigation/native';
import type { AllScreensParamList } from 'src/navigation/routes';
import type { AccreditationsParamList } from 'src/screens/Accreditations/accreditationsRoutes';

export const useAccreditationRoute = <
  Route extends keyof AccreditationsParamList,
>(): RouteProp<AllScreensParamList, Route> =>
  useRoute<RouteProp<AllScreensParamList, Route>>();

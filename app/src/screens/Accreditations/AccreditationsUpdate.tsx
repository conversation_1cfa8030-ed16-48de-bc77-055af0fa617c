import React from 'react';
import { ACCREDITATIONS_UPDATE_SCREEN } from 'src/constants';
import { CompanyAccreditationType } from 'src/data/schemas/firestore/companies';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { useAccreditation } from './hooks/useAccreditation';
import { useAccreditationRoute } from './hooks/useAccreditationRoute';
import { UpdateAccreditation } from './shared';

export const AccreditationsUpdate: React.FC = () => {
  const route = useAccreditationRoute<typeof ACCREDITATIONS_UPDATE_SCREEN>();
  const { id } = route.params ?? {};
  if (!id) {
    throw new InvalidNavParamsError();
  }

  const {
    data: companyAccreditation,
    updateAccreditation,
    deleteAccreditation,
  } = useAccreditation(id);

  const handleUpdate = async (accreditation: CompanyAccreditationType) => {
    await updateAccreditation(accreditation);
  };

  const handleDelete = async () => {
    await deleteAccreditation();
  };

  return (
    <UpdateAccreditation
      companyAccreditation={companyAccreditation}
      onDelete={handleDelete}
      onUpdate={handleUpdate}
    />
  );
};

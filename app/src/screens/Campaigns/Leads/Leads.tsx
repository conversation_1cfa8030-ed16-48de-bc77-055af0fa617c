import React, { useEffect, useState } from 'react';
import { View } from 'react-native';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { ButtonTabs } from '@cat-home-experts/react-native-components';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { createTestIds } from 'src/utilities/testIds';
import { FilterableLeadsList } from 'src/screens/Campaigns/components/LeadsList';
import { VIEW_LEADS_SCREEN } from 'src/constants';
import { AllScreensParamList } from 'src/navigation/routes';
import { Loader } from 'src/components';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { useFeatureFlag } from 'src/hooks/useFeatureFlag';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { useCampaignDetails } from '../hooks/useCampaignDetails/useCampaignDetails';
import { LeadsTrends } from '../components/LeadsTrends';
import { LEADS_LIST_COPY } from '../constants';

const TEST_IDS = createTestIds('leads', {});

type LeadsScreenTabValues = 'leads' | 'trends';

export const Leads: React.NativeFC<object, typeof TEST_IDS> = () => {
  const navigation = useNavigation();
  const isDesktop = useDesktopMediaQuery();

  const isLeadsTrendsEnabled = useFeatureFlag('enable_leads_trends', {
    enabledInDev: false,
    isFeatureComplete: true,
  });

  const { params } =
    useRoute<RouteProp<AllScreensParamList, typeof VIEW_LEADS_SCREEN>>();
  const { campaignId, dateGeneratedFrom, dateGeneratedTo } = params ?? {};
  if (!campaignId) {
    throw new InvalidNavParamsError();
  }

  const { campaignDetails, isLoading: isCampaignDetailsLoading } =
    useCampaignDetails(campaignId);

  useEffect(() => {
    navigation.setOptions({
      title: campaignDetails?.category.name,
    });
  }, [campaignDetails, navigation]);

  const [selectedTab, setSelectedTab] = useState<LeadsScreenTabValues>('leads');

  const LEADS_SCREEN_TABS = [
    {
      label: LEADS_LIST_COPY.LEADS,
      value: 'leads',
      component: (
        <FilterableLeadsList
          campaignId={campaignId}
          initialStartDate={dateGeneratedFrom}
          initialEndDate={dateGeneratedTo}
        />
      ),
    },
    {
      label: LEADS_LIST_COPY.TRENDS,
      value: 'trends',
      component: <LeadsTrends campaignId={campaignId} />,
    },
  ];

  if (isCampaignDetailsLoading) {
    return <Loader />;
  }

  return (
    <View style={styles.root} testID={TEST_IDS.ROOT}>
      <View style={styles.contentContainer}>
        {isLeadsTrendsEnabled && (
          <View style={[styles.tabs, isDesktop && styles.tabsDesktop]}>
            <ButtonTabs
              tabs={LEADS_SCREEN_TABS}
              selectedTab={selectedTab}
              setSelectedTab={(tab) =>
                setSelectedTab(tab as LeadsScreenTabValues)
              }
            />
          </View>
        )}

        {LEADS_SCREEN_TABS.find((tab) => tab.value === selectedTab)?.component}
      </View>
    </View>
  );
};

Leads.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  root: {
    flex: 1,
    backgroundColor: palette.mortarV3.tokenDefault100,
  },
  contentContainer: {
    maxWidth: 800,
    width: '100%',
    alignSelf: 'center',
    flex: 1,
    paddingTop: spacing(4),
  },
  tabs: {
    paddingHorizontal: spacing(2),
  },
  tabsDesktop: {
    paddingHorizontal: spacing(0),
  },
}));

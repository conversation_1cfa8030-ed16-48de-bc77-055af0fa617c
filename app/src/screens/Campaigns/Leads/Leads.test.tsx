import { cleanup, fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import { Leads } from 'src/screens/Campaigns/Leads';
import { useCampaignLeads } from 'src/screens/Campaigns/hooks/useCampaignLeads';
import { useFeatureFlag } from 'src/hooks/useFeatureFlag';
import { useCampaignDetails } from '../hooks/useCampaignDetails/useCampaignDetails';
import { FilterableLeadsList } from '../components/LeadsList';
import { LeadsTrends } from '../components/LeadsTrends';
import { LEADS_LIST_COPY } from '../constants';
import { useCampaignStatistics } from '../hooks/useCampaignStatistics/useCampaignStatistics';

const mockNavigate = jest.fn();
const mockSetOptions = jest.fn();
const mockUseRoute = jest.fn();

jest.mock('@react-navigation/native', () => {
  const actualNav = jest.requireActual('@react-navigation/native');

  return {
    ...actualNav,
    useNavigation: () => ({
      navigate: mockNavigate,
      setOptions: mockSetOptions,
    }),
    useRoute: mockUseRoute,
  };
});

jest.mock('@tanstack/react-query', () => {
  const actual = jest.requireActual('@tanstack/react-query');
  return {
    ...actual,
    useQueryClient: jest.fn,
  };
});

jest.mock('@gorhom/bottom-sheet', () =>
  jest.requireActual('@gorhom/bottom-sheet/mock'),
);

jest.mock('src/hooks/useFeatureFlag/useFeatureFlag');
jest.mock('../hooks/useCampaignLeads/useCampaignLeads');
jest.mock('../hooks/useCampaignDetails/useCampaignDetails');
jest.mock('../hooks/useCampaignStatistics/useCampaignStatistics');

describe('Leads', () => {
  beforeEach(() => {
    (mockUseRoute as jest.Mock).mockReturnValue({
      params: { campaignId: 'abc123' },
    });

    (useCampaignLeads as jest.Mock).mockReturnValue({
      leads: {},
      loading: false,
      error: undefined,
    });
    (useCampaignDetails as jest.Mock).mockReturnValue({
      campaignDetails: {
        category: {
          name: 'Category Name',
        },
      },
      loading: false,
      error: undefined,
    });

    (useCampaignStatistics as jest.Mock).mockReturnValue({
      campaignStatistics: {},
      loading: false,
      error: undefined,
    });

    (useFeatureFlag as jest.Mock).mockReturnValue(true);
  });

  afterEach(cleanup);

  it('should render the Leads page correctly when "campaignId" param is provided', () => {
    const { getByTestId } = render(<Leads />);
    const leadDetailsPage = getByTestId(Leads.testIds.ROOT);

    expect(leadDetailsPage).toBeDefined();
  });

  it('should call navigation setOptions with correct categoryName as title based on the campaign detail', () => {
    render(<Leads />);

    expect(mockSetOptions).toHaveBeenCalledWith({
      title: 'Category Name',
    });
  });

  it('should render the Leads tab by default', () => {
    const { getByText, getByTestId } = render(<Leads />);

    // Check that the Leads tab is selected
    expect(getByText(LEADS_LIST_COPY.LEADS)).toBeDefined();

    // Check that the FilterableLeadsList component is rendered
    expect(getByTestId(FilterableLeadsList.testIds.ROOT)).toBeDefined();
  });

  it('should switch to Trends tab when clicked', () => {
    const { getByText, queryByTestId, getByTestId } = render(<Leads />);

    // Initially, FilterableLeadsList should be visible and LeadsTrends should not
    expect(getByTestId(FilterableLeadsList.testIds.ROOT)).toBeDefined();
    expect(queryByTestId(LeadsTrends.testIds.ROOT)).toBeNull();

    // Click on the Trends tab
    fireEvent.press(getByText(LEADS_LIST_COPY.TRENDS));

    // Now LeadsTrends should be visible and FilterableLeadsList should not
    expect(getByTestId(LeadsTrends.testIds.ROOT)).toBeDefined();
    expect(queryByTestId(FilterableLeadsList.testIds.ROOT)).toBeNull();
  });

  it('should show loader when campaign details are loading', () => {
    (useCampaignDetails as jest.Mock).mockReturnValue({
      campaignDetails: null,
      isLoading: true,
      error: undefined,
    });

    const { getByTestId } = render(<Leads />);

    // Loader should be visible
    expect(getByTestId('loader')).toBeDefined();
  });
});

import React, { useCallback, useMemo } from 'react';
import { ScrollView, View } from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { format } from 'date-fns';
import {
  createMortarStyles,
  createTestIds,
  palette as staticPalette,
} from '@cat-home-experts/react-native-utilities';
import { InformationCircleFill } from '@cat-home-experts/mortar-iconography-native';
import {
  BasicAccordion,
  BulletList,
  Loader,
  TokenisedText,
  TokenisedTextProps,
  Typography,
} from '@cat-home-experts/react-native-components';
import {
  UPGRADE_CAMPAIGN_SCREEN,
  UPGRADE_CAMPAIGN_SUMMARY_SCREEN,
} from 'src/constants';
import { CampaignQuote } from 'src/data/schemas/api/campaigns';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { BrazeContentCard } from 'src/components/BrazeContentCard';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { useCampaignUpgrades } from '../hooks/useCampaignUpgrades';
import { useCampaignDetails } from '../hooks/useCampaignDetails/useCampaignDetails';
import { CategoryThumbnailWithSkeleton } from '../components';
import {
  getFixedCampaignDateRange,
  getFixedLeadCommitment,
} from '../utilities';
import { CampaignStatusDisplay } from '../components/CampaignStatusDisplay';
import { formatPrice } from '../utilities/formatPrice';
import { CampaignUpgradeOptionCard } from '../components/CampaignUpgradeOptionCard';
import { useIsFixedPlanUpgradesEnabled } from '../hooks/useIsFixedPlanUpgradesEnabled';
import { UPGRADE_CAMPAIGN } from '../constants';

const TEST_IDS = createTestIds('upgrade-campaign', {
  ACTIVE_DATE_RANGE: 'active-date-range',
  COST_PER_MONTH: 'cost-per-month',
  ESTIMATED_LEADS: 'estimated-leads',
  TITLE: 'title',
  ERROR_TITLE: 'error-title',
  ERROR_DESCRIPTION: 'error-description',
  UPGRADE_OPTIONS_CONTAINER: 'upgrade-options-container',
});

const COMPONENTS: TokenisedTextProps['components'] = {
  container: (props) => <Typography {...props} useVariant="labelRegular" />,
  text: (props) => <React.Fragment {...props} />,
  bold: (props) => <Typography useVariant="labelSemiBold" {...props} />,
};

export const UpgradeCampaign: React.NativeFC<object, typeof TEST_IDS> = () => {
  const isDesktop = useDesktopMediaQuery();

  const isFixedPlanUpgradesEnabled = useIsFixedPlanUpgradesEnabled();

  const route =
    useRoute<
      RouteProp<ReactNavigation.RootParamList, typeof UPGRADE_CAMPAIGN_SCREEN>
    >();
  const { campaignId } = route.params ?? {};
  if (!campaignId) {
    throw new InvalidNavParamsError();
  }

  const navigation = useNavigation();

  const { campaignDetails, isLoading: campaignDetailsLoading } =
    useCampaignDetails(campaignId);
  const { upgradeOptions, isLoading: isUpgradeOptionsLoading } =
    useCampaignUpgrades(campaignId, true);

  const headingDescriptionList = useMemo(
    () => [
      {
        title: UPGRADE_CAMPAIGN.DESCRIPTION_LIST.ACTIVE_DATE_RANGE,
        description: (
          <Typography
            useVariant="bodySMRegular"
            testID={TEST_IDS.ACTIVE_DATE_RANGE}
          >
            {getFixedCampaignDateRange(campaignDetails)}
          </Typography>
        ),
      },
      {
        title: UPGRADE_CAMPAIGN.DESCRIPTION_LIST.COST_PER_MONTH,
        description: (
          <Typography
            useVariant="bodySMSemiBold"
            testID={TEST_IDS.COST_PER_MONTH}
          >
            {formatPrice(campaignDetails?.fixed?.price ?? 0)}
            <Typography useVariant="bodySMRegular">
              {` ${UPGRADE_CAMPAIGN.DESCRIPTION_LIST.PLUS_VAT}`}
            </Typography>
          </Typography>
        ),
      },
      {
        title: UPGRADE_CAMPAIGN.DESCRIPTION_LIST.ESTIMATED_LEADS,
        description: (
          <Typography
            useVariant="bodySMSemiBold"
            testID={TEST_IDS.ESTIMATED_LEADS}
          >
            {getFixedLeadCommitment(campaignDetails)}
          </Typography>
        ),
      },
    ],
    [campaignDetails],
  );

  const optionsValidUntil = useMemo(() => format(new Date(), 'dd/MM/yyyy'), []);

  const onSelectUpgradeOption = useCallback(
    (upgradeOption: CampaignQuote) => {
      if (!upgradeOptions || typeof upgradeOptions === 'string') {
        return;
      }

      logEvent(EVENT_TYPE.CAMPAIGN_UPGRADES_SELECT_UPGRADE, {
        campaignId,
      });

      navigation.navigate(UPGRADE_CAMPAIGN_SUMMARY_SCREEN, {
        campaignId,
        quoteRequestId: upgradeOptions.quoteRequestId,
        quoteId: upgradeOption.quoteId,
      });
    },
    [navigation, campaignId, upgradeOptions],
  );

  if (campaignDetailsLoading) {
    return <Loader />;
  }

  if (!campaignDetails) {
    return null;
  }

  return (
    <ScrollView testID={TEST_IDS.ROOT}>
      <View style={styles.headingContainer}>
        <View style={styles.row}>
          <View style={styles.headingContainerLeft}>
            <CategoryThumbnailWithSkeleton
              categoryId={campaignDetails.category.categoryId}
            />
            <Typography useVariant="bodySemiBold" testID={TEST_IDS.TITLE}>
              {campaignDetails.category.name}
            </Typography>
          </View>
          <CampaignStatusDisplay campaign={campaignDetails} />
        </View>
        {isDesktop && <View style={styles.divider} />}
        <View style={styles.headingDescriptionList}>
          {headingDescriptionList.map((item) => (
            <View style={styles.row} key={item.title}>
              <Typography useVariant="labelRegular">{item.title}</Typography>
              {item.description}
            </View>
          ))}
        </View>
      </View>
      <View style={[styles.container, isDesktop && styles.desktopContainer]}>
        <BrazeContentCard
          placement="campaigns-upgrade-quote"
          style={[
            styles.brazeContentCard,
            isDesktop && styles.brazeContentCardDesktop,
          ]}
        />

        <View style={[styles.card, isDesktop && styles.cardDesktop]}>
          <View
            style={[styles.cardHeader, isDesktop && styles.cardHeaderDesktop]}
          >
            <Typography useVariant="bodySemiBold">
              {typeof upgradeOptions === 'string'
                ? UPGRADE_CAMPAIGN.UPGRADE_TEXT
                : UPGRADE_CAMPAIGN.SELECT_UPGRADE_TEXT}
            </Typography>
          </View>
          <View
            style={[
              styles.selectUpgradeContent,
              isDesktop && styles.selectUpgradeContentDesktop,
            ]}
          >
            {isUpgradeOptionsLoading ? (
              <Loader />
            ) : typeof upgradeOptions === 'string' ||
              !isFixedPlanUpgradesEnabled ? (
              <View
                style={[
                  styles.errorContainer,
                  !isDesktop && styles.errorContainerMobile,
                ]}
              >
                <View style={styles.errorContent}>
                  <View style={styles.errorContent}>
                    <InformationCircleFill
                      color={staticPalette.mortarV3.tokenDefault700}
                      size={40}
                    />

                    <Typography
                      useVariant="headingSMSemiBold"
                      isCentred
                      testID={TEST_IDS.ERROR_TITLE}
                    >
                      {UPGRADE_CAMPAIGN.ERROR_TITLE}
                    </Typography>
                    <Typography
                      useVariant="bodySMRegular"
                      isCentred
                      testID={TEST_IDS.ERROR_DESCRIPTION}
                    >
                      {UPGRADE_CAMPAIGN.ERROR_DESCRIPTION}
                    </Typography>
                  </View>
                </View>
              </View>
            ) : (
              <View
                style={styles.upgradeOptionsContainer}
                testID={TEST_IDS.UPGRADE_OPTIONS_CONTAINER}
              >
                {upgradeOptions?.quoteSet?.map((option) => (
                  <CampaignUpgradeOptionCard
                    key={option.quoteId}
                    upgradeOption={option}
                    onSelect={onSelectUpgradeOption}
                  />
                ))}

                <TokenisedText
                  value={UPGRADE_CAMPAIGN.QUOTE_VALID_UNTIL(optionsValidUntil)}
                  components={COMPONENTS}
                />
              </View>
            )}
          </View>
        </View>

        {!isDesktop && <View style={styles.divider} />}

        <View style={[styles.card, isDesktop && styles.cardDesktop]}>
          <View
            style={[styles.cardHeader, isDesktop && styles.cardHeaderDesktop]}
          >
            <Typography useVariant="bodySemiBold">
              {UPGRADE_CAMPAIGN.FAQ_TITLE}
            </Typography>
          </View>
          {isDesktop && <View style={styles.divider} />}
          <View
            style={[
              styles.faqsContainer,
              isDesktop && styles.faqsContainerDesktop,
            ]}
          >
            {UPGRADE_CAMPAIGN.FAQS.map((item) => (
              <BasicAccordion
                key={item.title}
                primaryItem={item.title}
                style={styles.faqsAccordion}
              >
                <View style={styles.faqContent}>
                  {item.paragraphs.map((paragraph) => (
                    <Typography key={paragraph} useVariant="bodySMRegular">
                      {paragraph}
                    </Typography>
                  ))}
                  {item.bullets && (
                    <BulletList
                      list={item.bullets.map((bullet) => [
                        {
                          value: bullet,
                          typographyVariant: 'bodySMRegular',
                        },
                      ])}
                    />
                  )}
                </View>
              </BasicAccordion>
            ))}
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

UpgradeCampaign.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  headingContainer: {
    backgroundColor: palette.mortarV3.tokenNeutral0,
    paddingHorizontal: spacing(3),
    paddingVertical: spacing(2),
    gap: spacing(1),
    borderBottomWidth: 1,
    borderBottomColor: palette.mortarV3.tokenNeutral200,
  },
  divider: {
    borderBottomWidth: 1,
    borderBottomColor: palette.mortarV3.tokenNeutral200,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headingContainerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headingDescriptionList: {
    paddingTop: spacing(1),
    gap: spacing(1),
  },
  container: {},
  desktopContainer: {
    paddingVertical: spacing(8),
    gap: spacing(4),
  },
  brazeContentCard: {
    maxWidth: 800,
    width: '100%',
    alignSelf: 'center',
    backgroundColor: palette.mortarV3.tokenNeutral100,
    paddingHorizontal: spacing(2.5),
    paddingVertical: spacing(2),
  },
  brazeContentCardDesktop: {
    paddingHorizontal: 0,
    paddingVertical: 0,
    marginTop: -spacing(3),
  },
  card: {
    maxWidth: 800,
    width: '100%',
    alignSelf: 'center',
    backgroundColor: palette.mortarV3.tokenNeutral0,
  },
  cardDesktop: {
    borderRadius: spacing(1),
    borderWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral200,
  },
  cardHeader: {
    padding: spacing(2.5),
  },
  cardHeaderDesktop: {
    borderBottomWidth: 1,
    borderBottomColor: palette.mortarV3.tokenNeutral200,
  },
  selectUpgradeContent: {
    paddingHorizontal: spacing(2.5),
    paddingBottom: spacing(4),
  },
  selectUpgradeContentDesktop: {
    paddingVertical: spacing(3),
  },
  upgradeOptionsContainer: {
    gap: spacing(2),
  },
  errorContainer: {
    paddingTop: spacing(2),
    paddingBottom: spacing(4),
    paddingHorizontal: spacing(2),
    gap: spacing(1),
  },
  errorContainerMobile: {
    borderRadius: spacing(1),
    borderWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral200,
  },
  errorContent: {
    alignItems: 'center',
    gap: spacing(2),
  },
  faqsContainer: {
    gap: spacing(2),
    paddingBottom: spacing(8),
    paddingHorizontal: spacing(2.5),
  },
  faqsContainerDesktop: {
    paddingTop: spacing(2.5),
    paddingBottom: spacing(2.5),
  },
  faqsAccordion: {
    borderRadius: spacing(0.5),
    borderColor: palette.mortarV3.tokenNeutral200,
  },
  faqContent: {
    paddingHorizontal: spacing(2),
    paddingBottom: spacing(2),
    gap: spacing(1),
  },
}));

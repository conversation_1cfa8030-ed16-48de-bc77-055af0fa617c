import React, { ReactElement, useMemo, useState } from 'react';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { ScrollView, View } from 'react-native';
import { add, addDays } from 'date-fns';

import { Loader, Typography } from '@cat-home-experts/react-native-components';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';

import { createTestIds } from 'src/utilities/testIds';
import { Separator, showToast, SimpleDatePicker } from 'src/components';
import { useCategories } from 'src/hooks/useCategories';
import { logEvent } from 'src/services/analytics';
import { AllScreensParamList } from 'src/navigation/routes';
import { PAUSE_CAMPAIGN_SCREEN } from 'src/constants';
import { ANALYTICS_ACTION_TYPE, EVENT_TYPE } from 'src/constants.events';
import { FixedTermCard } from 'src/screens/Campaigns/components/CampaignCard/FixedTermCard';
import { useCampaignDetails } from 'src/screens/Campaigns/hooks/useCampaignDetails/useCampaignDetails';
import { CampaignTypeEnum } from 'src/data/schemas/api/campaigns';
import { CampaignCard } from 'src/screens/Campaigns/components/CampaignCard/CampaignCard';
import { useUpdateCampaignDetails } from 'src/screens/Campaigns/hooks/useCampaignDetails/useUpdateCampaignDetails';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import {
  CAMPAIGN_DETAILS_REQUEST_RESPONSE_COPY,
  PAUSE_CAMPAIGN,
  PAUSE_CAMPAIGN_WARNING_TYPES,
  PauseCampaignWarningTypes,
} from '../constants';
import { BottomStickyButton } from '../components';
import { PauseCampaignWarningModal } from '../components/PauseCampaignWarningModal';

const TEST_IDS = createTestIds('my-campaigns-pause-campaign', {
  PAUSE_BUTTON: 'pause-button',
  DATE_PICKER: 'date-picker',
});

export function PauseCampaign(): ReactElement | null {
  const [isPauseButtonDisabled, setIsPauseButtonDisabled] = useState(false);
  const [warningType, setWarningType] =
    useState<PauseCampaignWarningTypes | null>(null);

  const navigation = useNavigation();

  const route =
    useRoute<RouteProp<AllScreensParamList, typeof PAUSE_CAMPAIGN_SCREEN>>();
  const { campaignId, categoryId } = route.params ?? {};
  if (!campaignId || !categoryId) {
    throw new InvalidNavParamsError();
  }

  const { toggleCampaignStatus } = useUpdateCampaignDetails();

  const {
    campaignDetails: campaignDetails,
    isLoading: isCampaignDetailsLoading,
  } = useCampaignDetails(campaignId);

  const { categories, isLoading: isCategoriesLoading } = useCategories({
    categoryIds: [categoryId],
  });

  const tomorrowsDate = useMemo(() => {
    return addDays(new Date(), 1);
  }, []);

  const [selectedDate, setSelectedDate] = useState<Date>(() => tomorrowsDate);

  const handleDatePickerConfirm = (date: Date) => {
    setSelectedDate(date);
  };

  const pauseCampaign = async (date: Date) => {
    try {
      setIsPauseButtonDisabled(true);
      await toggleCampaignStatus({ campaignId, pausedUntil: date });

      setSelectedDate(tomorrowsDate);
      logEvent(
        `${EVENT_TYPE.CAMPAIGN_PAUSE_SUCCESS}_${ANALYTICS_ACTION_TYPE.SUBMITTED}`,
        {
          pausedUntil: date,
          campaignId,
        },
      );
      navigation.goBack();
    } catch {
      showToast({
        text1: CAMPAIGN_DETAILS_REQUEST_RESPONSE_COPY.ERROR_UPDATE_CAMPAIGN,
        type: 'error',
      });
    } finally {
      setIsPauseButtonDisabled(false);
    }
  };

  const handleConfirmPause = async () => {
    if (selectedDate) {
      if (campaignDetails?.currentBudgetAndBalance.isMinimumCommitmentActive) {
        if (
          campaignDetails.currentBudgetAndBalance.balance <
          campaignDetails.currentBudgetAndBalance.minimumCommitmentAmount
        ) {
          setWarningType(PAUSE_CAMPAIGN_WARNING_TYPES.MINIMUM_COMMITMENT);
          return;
        }

        if (
          campaignDetails.currentBudgetAndBalance.balance <
          campaignDetails.currentBudgetAndBalance.minimumCommitmentAmount +
            campaignDetails.currentBudgetAndBalance.budgetBoostAmount
        ) {
          setWarningType(PAUSE_CAMPAIGN_WARNING_TYPES.BUDGET_BOOST);
          return;
        }
      }

      await pauseCampaign(selectedDate);
    }
  };

  const onConfirmWarning = async () => {
    if (warningType) {
      setWarningType(null);
      await pauseCampaign(selectedDate);
    }
  };

  const onDismissWarning = () => {
    setWarningType(null);
  };

  if (isCampaignDetailsLoading || isCategoriesLoading || !campaignDetails) {
    return <Loader />;
  }

  return (
    <>
      <ScrollView testID={TEST_IDS.ROOT} style={styles.wrapper}>
        <View style={styles.container}>
          <View style={styles.headerContainer}>
            <Typography useVariant="headingSMSemiBold">
              {PAUSE_CAMPAIGN.TITLE_PART_1}
              <Typography
                useVariant="headingSMSemiBold"
                style={styles.blueText}
              >
                {categories[0].name}
              </Typography>
              {PAUSE_CAMPAIGN.TITLE_PART_2}
            </Typography>
            <Typography style={styles.topMargin} useVariant="bodyRegular">
              {campaignDetails.campaignType === CampaignTypeEnum.Ppl
                ? PAUSE_CAMPAIGN.PPL_DESCRIPTION
                : PAUSE_CAMPAIGN.FIXED_DESCRIPTION}
            </Typography>
          </View>
          <Separator />
          {campaignDetails.campaignType === CampaignTypeEnum.Ppl ? (
            <CampaignCard
              showStatus={false}
              campaign={campaignDetails}
              categoryName={campaignDetails.category.name!}
              daysLeftTillBudgetReset={
                campaignDetails.currentBudgetAndBalance.daysRemainingInPeriod +
                1
              }
              containerStylesOverride={styles.campaignCard}
            />
          ) : (
            <FixedTermCard
              showStatus={false}
              showLeadExpectation={false}
              campaign={campaignDetails}
              categoryName={campaignDetails.category.name!}
              containerStylesOverride={styles.campaignCard}
            />
          )}

          <Typography style={styles.topMargin} useVariant="bodyMedium">
            {PAUSE_CAMPAIGN.DATE_PICKER_PROMPT}
          </Typography>
          <SimpleDatePicker
            testID={TEST_IDS.DATE_PICKER}
            placeholder={
              selectedDate ? undefined : PAUSE_CAMPAIGN.DATE_PICKER_PLACEHOLDER
            }
            shownDate={selectedDate}
            minimumDate={add(new Date(), { days: 1 })}
            maximumDate={add(new Date(), { days: 14 })}
            onConfirm={handleDatePickerConfirm}
            contentContainerStyle={styles.datePicker}
          />
        </View>
        <BottomStickyButton
          isDisabled={isPauseButtonDisabled}
          handleOnPress={handleConfirmPause}
          label={PAUSE_CAMPAIGN.PAUSE_BUTTON_LABEL}
          testID={TEST_IDS.PAUSE_BUTTON}
          isButtonVisible={Boolean(selectedDate)}
        />
      </ScrollView>
      <PauseCampaignWarningModal
        onConfirm={onConfirmWarning}
        onDismiss={onDismissWarning}
        warningType={warningType}
      />
    </>
  );
}

PauseCampaign.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  wrapper: {
    flex: 1,
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    width: '100%',
    height: '100%',
  },
  container: {
    maxWidth: 800,
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    paddingVertical: spacing(4),
    alignSelf: 'center',
    paddingHorizontal: spacing(3),
    zIndex: 2,
  },
  headerContainer: {
    paddingBottom: spacing(4),
  },
  blueText: {
    color: palette.mortar.tokenColorSystemLinkBlue,
  },
  topMargin: { marginTop: spacing(1) },
  campaignCard: {
    paddingHorizontal: spacing(0),
  },
  datePicker: { paddingHorizontal: spacing(0), marginTop: spacing(2) },
}));

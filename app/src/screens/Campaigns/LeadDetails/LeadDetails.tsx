import React, { useCallback, useMemo } from 'react';
import { View } from 'react-native';
import { differenceInDays } from 'date-fns';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import {
  createMortarStyles,
  createTestIds,
  isTruthy,
  spacing,
} from '@cat-home-experts/react-native-utilities';
import { Button, Typography } from '@cat-home-experts/react-native-components';
import {
  MARKETPLACE_JOB_DETAILS_SCREEN,
  JOBS_SCREEN,
  REPORT_LEAD_SCREEN,
  VIEW_LEAD_DETAILS_SCREEN,
} from 'src/constants';
import { useLeadDetails } from 'src/screens/Campaigns/hooks/useLeadDetails/useLeadDetails';
import { useMarketplaceJobDetails } from 'src/screens/MarketplaceJobs/hooks/useMarketplaceJobDetails';
import { PageNotFound } from 'src/screens/PageNotFound';
import { Loader } from 'src/components';
import { LEAD_DETAILS_DISPUTE_COPY } from 'src/screens/Campaigns/constants';
import { CampaignTypeEnum } from 'src/data/schemas/api/campaigns';
import { LeadDetail } from 'src/screens/Campaigns/components/LeadDetail';
import { useFeatureFlagWithBetaTesters } from 'src/hooks/useFeatureFlagWithBetaTesters';
import { LeadChannelType } from 'src/data/schemas/api/campaigns/LeadDtoPageResult';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { useCampaignDetails } from '../hooks/useCampaignDetails/useCampaignDetails';

const TEST_IDS = createTestIds('lead-details', {
  OPEN_LEAD_IN_JOB_BUTTON: 'lead-job-button',
});

export const LeadDetails: React.NativeFC<object, typeof TEST_IDS> = () => {
  const leadDisputesEnabled = useFeatureFlagWithBetaTesters({
    featureFlagName: 'enable_lead_disputes_v1',
    betaTesterPoolName: 'enable_lead_disputes_test_companies',
    isFeatureComplete: true,
  });

  const { params } =
    useRoute<
      RouteProp<ReactNavigation.RootParamList, typeof VIEW_LEAD_DETAILS_SCREEN>
    >();

  const navigation = useNavigation();

  const { leadId, campaignId } = params ?? {};
  if (!leadId || !campaignId) {
    throw new InvalidNavParamsError();
  }

  const { lead, loading: isLeadLoading } = useLeadDetails(leadId);
  const { campaignDetails, isLoading: isCampaignDetailsLoading } =
    useCampaignDetails(campaignId);
  const { job, isLoading: isJobLoading } = useMarketplaceJobDetails(
    lead?.jobIdV2,
  );

  const isWithinDisputeWindow = useMemo(() => {
    if (!lead) {
      return true;
    }

    return differenceInDays(new Date(), lead.dateGenerated) <= 60;
  }, [lead]);

  const isDisputeLeadButtonDisabled = useMemo(() => {
    return leadDisputesEnabled && (lead?.isDisputed || !isWithinDisputeWindow);
  }, [lead, leadDisputesEnabled, isWithinDisputeWindow]);

  const shouldShowOpenLeadButton =
    lead?.channel !== LeadChannelType.Click &&
    lead?.channel !== LeadChannelType.Call;

  const handleDisputeLeadPress = useCallback(() => {
    navigation.navigate(REPORT_LEAD_SCREEN, {
      leadId: leadId,
      campaignId: campaignId.toString(),
    });
  }, [campaignId, leadId, navigation]);

  const handleOpenLeadInJobsPress = useCallback(() => {
    if (!isTruthy(lead) || !shouldShowOpenLeadButton) {
      return;
    }

    if (
      lead.channel !== LeadChannelType.Message &&
      lead.channel !== LeadChannelType.RequestAQuote
    ) {
      navigation.navigate(JOBS_SCREEN);
      return;
    }

    if (isTruthy(job)) {
      navigation.navigate(MARKETPLACE_JOB_DETAILS_SCREEN, { id: job.id });
      return;
    }

    // Fallback if job is falsy
    navigation.navigate(JOBS_SCREEN);
  }, [job, lead, navigation, shouldShowOpenLeadButton]);

  if (isLeadLoading || isJobLoading || isCampaignDetailsLoading) {
    return <Loader />;
  }

  if (!lead) {
    return <PageNotFound fallbackScreenToNavigate={VIEW_LEAD_DETAILS_SCREEN} />;
  }

  const reportLeadButtonText = lead.isDisputed
    ? LEAD_DETAILS_DISPUTE_COPY.REPORTED
    : LEAD_DETAILS_DISPUTE_COPY.DISPUTE_LEAD;

  return (
    <View
      style={[styles.flex, styles.lightBlueBackground]}
      testID={TEST_IDS.ROOT}
    >
      <View style={[styles.flex, styles.mainContentContainer]}>
        <View style={styles.mainContent}>
          <LeadDetail
            lead={lead}
            job={job}
            campaignType={campaignDetails?.campaignType}
          />
        </View>
        <View style={styles.disputeButtonContainer}>
          {campaignDetails?.campaignType === CampaignTypeEnum.Ppl &&
            isDisputeLeadButtonDisabled &&
            !isWithinDisputeWindow && (
              <View style={styles.disputeButtonMessageContainer}>
                <Typography
                  useVariant="bodySemiBold"
                  style={styles.disputeButtonMessage}
                >
                  {LEAD_DETAILS_DISPUTE_COPY.OUT_OF_DISPUTE_WINDOW}
                </Typography>
              </View>
            )}
          <View style={styles.whiteBackground}>
            {shouldShowOpenLeadButton && (
              <Button
                block
                label={LEAD_DETAILS_DISPUTE_COPY.OPEN_LEAD_IN_JOBS}
                onPress={handleOpenLeadInJobsPress}
                variant="secondary"
                style={styles.button}
                testID={TEST_IDS.OPEN_LEAD_IN_JOB_BUTTON}
              />
            )}
            {campaignDetails?.campaignType === CampaignTypeEnum.Ppl &&
              leadDisputesEnabled && (
                <Button
                  block
                  label={reportLeadButtonText}
                  onPress={handleDisputeLeadPress}
                  variant="tertiary"
                  style={styles.button}
                  isDisabled={isDisputeLeadButtonDisabled}
                />
              )}
          </View>
        </View>
      </View>
    </View>
  );
};

LeadDetails.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette }) => ({
  flex: { flex: 1 },
  lightBlueBackground: {
    backgroundColor: palette.mortar.tokenColorLightBlue,
  },
  whiteBackground: {
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  borderAround: {
    borderColor: palette.system.tokenLegacyColorBorderDecorative,
  },
  mainContentContainer: {
    maxWidth: 800,
    width: '100%',
    alignSelf: 'center',
  },
  mainContent: {
    maxWidth: 800,
    width: '100%',
    alignSelf: 'center',
    paddingVertical: spacing(2),
    paddingLeft: spacing(3),
    marginTop: spacing(1),
    flexDirection: 'column',
    color: palette.mortar.tokenColorBlack,
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    borderTopWidth: 1,
    borderTopColor: palette.system.tokenLegacyColorSurfaceDisabled,
  },
  disputeButtonContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    paddingHorizontal: 25,
    marginBottom: spacing(6),
  },
  disputeButtonMessageContainer: {
    marginBottom: spacing(2),
    paddingHorizontal: spacing(2),
    alignItems: 'center',
  },
  disputeButtonMessage: {
    textAlign: 'center',
  },
  button: {
    marginBottom: spacing(1),
  },
}));

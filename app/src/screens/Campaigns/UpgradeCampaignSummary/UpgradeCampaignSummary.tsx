import React, { useMemo, useState } from 'react';
import { ScrollView, View } from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import {
  BasicAccordion,
  Button,
  Checkbox,
  Loader,
  Typography,
} from '@cat-home-experts/react-native-components';
import {
  CAMPAIGNS_SCREEN,
  MEMBERSHIP_TERMS_AND_CONDITIONS_URL,
  UPGRADE_CAMPAIGN_SUMMARY_SCREEN,
  VAT_RATE,
} from 'src/constants';
import { openURL } from 'src/utilities/linking';
import { ErrorScreen } from 'src/screens/ErrorScreen';
import { captureException } from 'src/services/datadog';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { showToast } from 'src/components';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { useCampaignDetails } from '../hooks/useCampaignDetails/useCampaignDetails';
import { CategoryThumbnailWithSkeleton } from '../components';
import {
  getFixedLeadCommitment,
  getFixedLeadCommitmentRemaining,
  getRenewalDateMonthsRemaining,
  getUpgradeOptionLeadForecast,
  getUpgradeOptionLeadForecastUpgradeDelta,
} from '../utilities';
import { useQuoteRequest } from '../hooks/useQuoteRequest';
import { formatPrice } from '../utilities/formatPrice';
import { useCampaignUpgrades } from '../hooks/useCampaignUpgrades';
import { UPGRADE_CAMPAIGN_SUMMARY } from '../constants';
import { useIsFixedPlanUpgradesEnabled } from '../hooks/useIsFixedPlanUpgradesEnabled';

interface UpgradeCampaignSummaryProps {
  isPagedComponent?: boolean;
  campaignId?: string;
  quoteId?: string;
  quoteRequestId?: string;
  navigateToMyCampaigns?: () => void;
}

const TEST_IDS = createTestIds('upgrade-campaign-summary', {
  TITLE: 'title',
  CURRENT_PLAN_ESTIMATED_LEADS: 'current-plan-estimated-leads',
  NEW_PLAN_ESTIMATED_LEADS: 'new-plan-estimated-leads',
  CURRENT_PLAN_PRICE: 'current-plan-price',
  NEW_PLAN_PRICE: 'new-plan-price',
  CURRENT_PLAN_PRICE_INC_VAT: 'current-plan-price-inc-vat',
  NEW_PLAN_PRICE_INC_VAT: 'new-plan-price-inc-vat',
  CURRENT_PLAN_LEADS_RECEIVED: 'current-plan-leads-received',
  NEW_PLAN_LEADS_RECEIVED: 'new-plan-leads-received',
  CURRENT_PLAN_PROJECTED_LEADS_REMAINING:
    'current-plan-projected-leads-remaining',
  NEW_PLAN_PROJECTED_LEADS_REMAINING: 'new-plan-projected-leads-remaining',
  CURRENT_PLAN_REMAINING_TERM: 'current-plan-remaining-term',
  NEW_PLAN_REMAINING_TERM: 'new-plan-remaining-term',
  TERMS_CHECKBOX: 'terms-checkbox',
  DISCLAIMER_LINK: 'disclaimer-link',
  UPGRADE_BUTTON: 'upgrade-button',
  ERROR_SCREEN: 'error-screen',
});

const IMAGE_SIZE = 85;

type NavRoute = RouteProp<
  ReactNavigation.RootParamList,
  typeof UPGRADE_CAMPAIGN_SUMMARY_SCREEN
>;

export const UpgradeCampaignSummary: React.NativeFC<
  UpgradeCampaignSummaryProps,
  typeof TEST_IDS
> = ({
  isPagedComponent,
  campaignId,
  quoteId,
  quoteRequestId,
  navigateToMyCampaigns,
}) => {
  const isDesktop = useDesktopMediaQuery();

  const isFixedPlanUpgradesEnabled = useIsFixedPlanUpgradesEnabled();

  const navigation = useNavigation();
  const route = useRoute<NavRoute>();
  const params = (route.params ?? {}) as NonNullable<NavRoute['params']>;
  if (
    !isPagedComponent &&
    (!params.campaignId || !params.quoteId || !params.quoteRequestId)
  ) {
    throw new InvalidNavParamsError();
  }

  const effectiveCampaignId = isPagedComponent ? campaignId : params.campaignId;
  const effectiveQuoteId = isPagedComponent ? quoteId : params.quoteId;
  const effectiveQuoteRequestId = isPagedComponent
    ? quoteRequestId
    : params.quoteRequestId;

  const { campaignDetails, isLoading: campaignDetailsLoading } =
    useCampaignDetails(effectiveCampaignId);
  const { upgradeCampaign } = useCampaignUpgrades();
  const { quote, isLoading: isQuoteLoading } = useQuoteRequest(
    effectiveQuoteRequestId,
  );

  const [isLoading, setIsLoading] = useState(false);
  const [isTermsAccepted, setIsTermsAccepted] = useState(false);

  const selectedUpgradeOption = useMemo(() => {
    if (!quote) {
      return null;
    }

    return quote?.quoteSet.find(
      (option) => option.quoteId === effectiveQuoteId,
    );
  }, [quote, effectiveQuoteId]);

  const quoteDescriptionList = useMemo(() => {
    if (!selectedUpgradeOption || !campaignDetails) {
      return [];
    }

    return [
      {
        label: UPGRADE_CAMPAIGN_SUMMARY.CURRENT_PLAN_ESTIMATED_LEADS,
        currentPlan: (
          <Typography
            key="currentPlanEstimatedLeads"
            isCentred
            useVariant="headingMDSemiBold"
            testID={TEST_IDS.CURRENT_PLAN_ESTIMATED_LEADS}
          >
            {getFixedLeadCommitment(campaignDetails)}
          </Typography>
        ),
        newPlan: (
          <Typography
            key="newPlanEstimatedLeads"
            isCentred
            useVariant="headingMDSemiBold"
            testID={TEST_IDS.NEW_PLAN_ESTIMATED_LEADS}
          >
            {getUpgradeOptionLeadForecast(selectedUpgradeOption)}
          </Typography>
        ),
      },
      {
        isDivider: true,
      },
      {
        label: UPGRADE_CAMPAIGN_SUMMARY.CURRENT_PLAN_PRICE,
        currentPlan: (
          <Typography
            key="currentPlanPrice"
            isCentred
            useVariant="bodySMSemiBold"
            testID={TEST_IDS.CURRENT_PLAN_PRICE}
          >
            {formatPrice(campaignDetails?.fixed?.price ?? 0)}
            <Typography useVariant="bodySMRegular">
              {UPGRADE_CAMPAIGN_SUMMARY.SPACE_PER_MONTH}
            </Typography>
          </Typography>
        ),
        newPlan: (
          <Typography
            key="newPlanPrice"
            isCentred
            useVariant="bodySMSemiBold"
            testID={TEST_IDS.NEW_PLAN_PRICE}
          >
            {formatPrice(
              selectedUpgradeOption.fixedDetails?.pricePerMonth ?? 0,
            )}
            <Typography useVariant="bodySMRegular">
              {UPGRADE_CAMPAIGN_SUMMARY.SPACE_PER_MONTH}
            </Typography>
          </Typography>
        ),
      },
      {
        label: UPGRADE_CAMPAIGN_SUMMARY.NEW_PLAN_PRICE,
        currentPlan: (
          <Typography
            key="currentPlanPriceIncVAT"
            isCentred
            useVariant="bodySMSemiBold"
            testID={TEST_IDS.CURRENT_PLAN_PRICE_INC_VAT}
          >
            {formatPrice((campaignDetails?.fixed?.price ?? 0) * VAT_RATE)}
            <Typography useVariant="bodySMRegular">
              {UPGRADE_CAMPAIGN_SUMMARY.SPACE_PER_MONTH}
            </Typography>
          </Typography>
        ),
        newPlan: (
          <Typography
            key="newPlanPriceIncVAT"
            isCentred
            useVariant="bodySMSemiBold"
            testID={TEST_IDS.NEW_PLAN_PRICE_INC_VAT}
          >
            {formatPrice(
              (selectedUpgradeOption.fixedDetails?.pricePerMonth ?? 0) *
                VAT_RATE,
            )}
            <Typography useVariant="bodySMRegular">
              {UPGRADE_CAMPAIGN_SUMMARY.SPACE_PER_MONTH}
            </Typography>
          </Typography>
        ),
      },
      {
        isDivider: true,
      },
      {
        label: UPGRADE_CAMPAIGN_SUMMARY.LEADS_RECEIVED,
        currentPlan: (
          <Typography
            key="currentPlanLeadsReceived"
            isCentred
            useVariant="bodySMSemiBold"
            testID={TEST_IDS.CURRENT_PLAN_LEADS_RECEIVED}
          >
            {campaignDetails.currentBudgetAndBalance.cumulativeLeadCount}
          </Typography>
        ),
        newPlan: (
          <Typography
            key="newPlanLeadsReceived"
            isCentred
            useVariant="bodySMSemiBold"
            testID={TEST_IDS.NEW_PLAN_LEADS_RECEIVED}
          >
            {campaignDetails.currentBudgetAndBalance.cumulativeLeadCount}
          </Typography>
        ),
      },
      {
        label: UPGRADE_CAMPAIGN_SUMMARY.PROJECTED_LEADS_REMAINING,
        currentPlan: (
          <Typography
            key="currentPlanProjectedLeadsRemaining"
            isCentred
            useVariant="bodySMSemiBold"
            testID={TEST_IDS.CURRENT_PLAN_PROJECTED_LEADS_REMAINING}
          >
            {getFixedLeadCommitmentRemaining(campaignDetails)}
          </Typography>
        ),
        newPlan: (
          <Typography
            key="newPlanProjectedLeadsRemaining"
            isCentred
            useVariant="bodySMSemiBold"
            testID={TEST_IDS.NEW_PLAN_PROJECTED_LEADS_REMAINING}
          >
            {isPagedComponent
              ? getUpgradeOptionLeadForecast(selectedUpgradeOption)
              : getUpgradeOptionLeadForecastUpgradeDelta(selectedUpgradeOption)}
          </Typography>
        ),
      },
      {
        label: UPGRADE_CAMPAIGN_SUMMARY.REMAINING_TERM,
        currentPlan: (
          <Typography
            key="currentPlanRemainingTerm"
            isCentred
            useVariant="bodySMSemiBold"
            testID={TEST_IDS.CURRENT_PLAN_REMAINING_TERM}
          >
            {getRenewalDateMonthsRemaining(campaignDetails)}{' '}
            {UPGRADE_CAMPAIGN_SUMMARY.MONTHS}
          </Typography>
        ),
        newPlan: (
          <Typography
            key="newPlanRemainingTerm"
            isCentred
            useVariant="bodySMSemiBold"
            testID={TEST_IDS.NEW_PLAN_REMAINING_TERM}
          >
            {getRenewalDateMonthsRemaining(campaignDetails)}{' '}
            {UPGRADE_CAMPAIGN_SUMMARY.MONTHS}
          </Typography>
        ),
      },
    ];
  }, [campaignDetails, selectedUpgradeOption, isPagedComponent]);

  const onUpgradePress = async () => {
    if (!effectiveQuoteRequestId || !effectiveQuoteId) {
      return;
    }

    setIsLoading(true);

    try {
      const { upgradeId } = await upgradeCampaign({
        campaignId: effectiveCampaignId as string,
        quoteRequestId: effectiveQuoteRequestId,
        quoteId: selectedUpgradeOption?.quoteId as string,
      });

      logEvent(EVENT_TYPE.CAMPAIGN_UPGRADES_SUCCESS, {
        campaignId: effectiveCampaignId,
        upgradeId,
      });

      setIsLoading(false);

      showToast({
        text1: UPGRADE_CAMPAIGN_SUMMARY.TOAST_SUCCESS_TITLE,
        type: 'success',
      });
      if (isPagedComponent) {
        navigateToMyCampaigns?.();
      } else {
        navigation.navigate(CAMPAIGNS_SCREEN);
      }
    } catch (error) {
      captureException(error, {
        source: 'UpgradeCampaignSummary',
      });

      showToast({
        text1: UPGRADE_CAMPAIGN_SUMMARY.TOAST_ERROR_TITLE,
        type: 'error',
      });

      setIsLoading(false);
    }
  };

  const onTermsPress = () => {
    openURL(MEMBERSHIP_TERMS_AND_CONDITIONS_URL);
  };

  const onTermsCheckboxPress = () => {
    setIsTermsAccepted((isTermsCurrentlyAccepted) => !isTermsCurrentlyAccepted);
  };

  const renderTermsCheckbox = () => (
    <View style={styles.termsCheckboxContainer}>
      <Checkbox
        isChecked={isTermsAccepted}
        onPress={onTermsCheckboxPress}
        testID={TEST_IDS.TERMS_CHECKBOX}
      />

      <Typography useVariant="labelRegular" style={styles.termsCheckboxText}>
        {UPGRADE_CAMPAIGN_SUMMARY.ACCEPT_TERMS_TEXTS[0]}
        <Typography
          useVariant="labelSemiBold"
          style={styles.disclaimerLink}
          onPress={onTermsPress}
          testID={TEST_IDS.DISCLAIMER_LINK}
        >
          {UPGRADE_CAMPAIGN_SUMMARY.ACCEPT_TERMS_TEXTS[1]}
        </Typography>
        {UPGRADE_CAMPAIGN_SUMMARY.ACCEPT_TERMS_TEXTS[2]}
      </Typography>
    </View>
  );

  const renderDowngradeWarning = () => (
    <View style={styles.downgradeWarningContainer}>
      <Typography useVariant="labelSMRegular" isCentred>
        {UPGRADE_CAMPAIGN_SUMMARY.DOWNGRADE_WARNING}
      </Typography>
    </View>
  );

  if (campaignDetailsLoading || isQuoteLoading) {
    return <Loader />;
  }

  if (
    !campaignDetails ||
    !selectedUpgradeOption ||
    !isFixedPlanUpgradesEnabled
  ) {
    return (
      <ErrorScreen
        testID={TEST_IDS.ERROR_SCREEN}
        title={UPGRADE_CAMPAIGN_SUMMARY.ERROR_TITLE}
        message={UPGRADE_CAMPAIGN_SUMMARY.ERROR_DESCRIPTION}
      />
    );
  }

  return (
    <ScrollView testID={TEST_IDS.ROOT} contentContainerStyle={styles.root}>
      <View style={styles.container}>
        <View
          style={[
            styles.headingContainer,
            isDesktop && styles.headingContainerDesktop,
          ]}
        >
          <CategoryThumbnailWithSkeleton
            categoryId={campaignDetails.category.categoryId}
            width={IMAGE_SIZE}
            height={IMAGE_SIZE}
            customStyle={styles.categoryImage}
          />
          <Typography
            isCentred
            useVariant="subHeadingSemiBold"
            testID={TEST_IDS.TITLE}
          >
            {campaignDetails.category.name}
          </Typography>
        </View>
        <View
          style={[
            styles.contentContainer,
            isDesktop && styles.contentContainerDesktop,
          ]}
        >
          {isDesktop ? (
            <View style={styles.contentContainerInnerDesktop}>
              <View
                style={[
                  styles.contentContainerDesktopInnerItem,
                  styles.contentContainerDesktopInnerItemLabels,
                ]}
              >
                {/* Alignment hacks */}
                <Typography isCentred useVariant="subHeadingSemiBold">
                  {' '}
                </Typography>
                <Typography useVariant="headingMDSemiBold">
                  <Typography useVariant="bodySMRegular">
                    {quoteDescriptionList[0].label}
                  </Typography>
                </Typography>
                {quoteDescriptionList.slice(1).map((item, i) => {
                  if (item.isDivider) {
                    // eslint-disable-next-line react/no-array-index-key
                    return <View key={i} style={styles.labelsDivider} />;
                  }

                  return (
                    <Typography key={item.label} useVariant="bodySMRegular">
                      {item.label}
                    </Typography>
                  );
                })}
              </View>
              <View
                style={[
                  styles.contentContainerDesktopInnerItem,
                  styles.contentContainerDesktopInnerItemCurrentPlan,
                ]}
              >
                <Typography isCentred useVariant="subHeadingSemiBold">
                  {UPGRADE_CAMPAIGN_SUMMARY.YOUR_CURRENT_PLAN}
                </Typography>
                {quoteDescriptionList.map((item, i) => {
                  if (item.isDivider) {
                    // eslint-disable-next-line react/no-array-index-key
                    return <View key={i} style={styles.labelsDivider} />;
                  }

                  return item.currentPlan;
                })}
              </View>
              <View
                style={[
                  styles.contentContainerDesktopInnerItem,
                  styles.contentContainerDesktopInnerItemNewPlan,
                ]}
              >
                <Typography isCentred useVariant="subHeadingSemiBold">
                  {UPGRADE_CAMPAIGN_SUMMARY.YOUR_NEW_PLAN}
                </Typography>
                {quoteDescriptionList.map((item, i) => {
                  if (item.isDivider) {
                    // eslint-disable-next-line react/no-array-index-key
                    return <View key={i} style={styles.labelsDivider} />;
                  }

                  return item.newPlan;
                })}
                {renderDowngradeWarning()}
                <Button
                  label="Upgrade"
                  size="small"
                  onPress={onUpgradePress}
                  block
                  isDisabled={!isTermsAccepted}
                  testID={TEST_IDS.UPGRADE_BUTTON}
                />
                {renderTermsCheckbox()}
              </View>
            </View>
          ) : (
            <View style={styles.contentContainerInnerMobile}>
              <Typography useVariant="subHeadingSemiBold">
                {UPGRADE_CAMPAIGN_SUMMARY.YOUR_NEW_PLAN}
              </Typography>
              <View style={styles.contentContainerInnerMobileItem}>
                {quoteDescriptionList.map((item, i) => {
                  if (item.isDivider) {
                    // eslint-disable-next-line react/no-array-index-key
                    return <View key={i} style={styles.labelsDivider} />;
                  }

                  return (
                    <View
                      style={styles.contentContainerInnerMobileItemContent}
                      key={item.label}
                    >
                      <Typography useVariant="labelRegular">
                        {item.label}
                      </Typography>
                      {item.newPlan}
                    </View>
                  );
                })}
              </View>
              <View style={styles.upgradeButtonContainerMobile}>
                {renderDowngradeWarning()}
                {renderTermsCheckbox()}
                <Button
                  label={UPGRADE_CAMPAIGN_SUMMARY.UPGRADE_TEXT}
                  size="small"
                  onPress={onUpgradePress}
                  block
                  isLoading={isLoading}
                  isDisabled={!isTermsAccepted}
                  testID={TEST_IDS.UPGRADE_BUTTON}
                />
              </View>
            </View>
          )}
        </View>

        {!isDesktop && (
          <View
            style={[styles.contentContainer, styles.mobileCurrentPlanContainer]}
          >
            <BasicAccordion
              primaryItem={UPGRADE_CAMPAIGN_SUMMARY.YOUR_CURRENT_PLAN}
              primaryItemVariant="subHeadingSemiBold"
              style={styles.currentPlanAccordion}
              headerChildren={
                <View
                  style={[
                    styles.contentContainerInnerMobileItemContent,
                    styles.currentPlanAccordionHeaderItem,
                  ]}
                  key={quoteDescriptionList[0].label}
                >
                  <Typography useVariant="labelRegular">
                    {quoteDescriptionList[0].label}
                  </Typography>
                  {quoteDescriptionList[0].currentPlan}
                </View>
              }
            >
              <View
                style={[
                  styles.contentContainerInnerMobileItem,
                  styles.currentPlanAccordionContent,
                ]}
              >
                {quoteDescriptionList.slice(1).map((item, i) => {
                  if (item.isDivider) {
                    // eslint-disable-next-line react/no-array-index-key
                    return <View key={i} style={styles.labelsDivider} />;
                  }

                  return (
                    <View
                      style={styles.contentContainerInnerMobileItemContent}
                      key={item.label}
                    >
                      <Typography useVariant="labelRegular">
                        {item.label}
                      </Typography>
                      {item.currentPlan}
                    </View>
                  );
                })}
              </View>
            </BasicAccordion>
          </View>
        )}

        <View style={styles.disclaimerContainer}>
          <View style={styles.divider} />
          <Typography useVariant="bodySMRegular">
            {UPGRADE_CAMPAIGN_SUMMARY.DISCLAIMER_TEXT}
          </Typography>
          <Typography useVariant="bodySMRegular">
            <Typography
              useVariant="bodySMSemiBold"
              style={styles.disclaimerLink}
              onPress={onTermsPress}
              testID={TEST_IDS.DISCLAIMER_LINK}
            >
              {UPGRADE_CAMPAIGN_SUMMARY.DISCLAIMER_LINK_TEXT}
            </Typography>{' '}
            {UPGRADE_CAMPAIGN_SUMMARY.DISCLAUMER_TERMS_TEXT}
          </Typography>
        </View>
      </View>
    </ScrollView>
  );
};

UpgradeCampaignSummary.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  root: {
    paddingBottom: spacing(4),
  },
  container: {
    maxWidth: 800,
    width: '100%',
    alignSelf: 'center',
    paddingHorizontal: spacing(2.5),
  },
  headingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing(2),
    paddingVertical: spacing(3),
  },
  headingContainerDesktop: {
    paddingTop: spacing(7.5),
    paddingBottom: spacing(5),
  },
  categoryImage: {
    width: IMAGE_SIZE,
    height: IMAGE_SIZE,
    marginRight: 0,
  },
  contentContainer: {
    backgroundColor: palette.mortarV3.tokenNeutral0,
    paddingHorizontal: spacing(1.5),
    paddingVertical: spacing(2),
    borderRadius: spacing(1),
    borderWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral200,
  },
  contentContainerDesktop: {
    paddingHorizontal: spacing(4),
    paddingVertical: spacing(4),
  },
  contentContainerInnerDesktop: {
    flexDirection: 'row',
    gap: spacing(1),
  },
  contentContainerDesktopInnerItem: {
    flex: 1,
    padding: spacing(3),
    gap: spacing(1.5),
  },
  labelsDivider: {
    height: 1,
    backgroundColor: palette.mortarV3.tokenNeutral200,
  },
  divider: {
    height: 1,
    backgroundColor: palette.mortarV3.tokenNeutral200,
  },
  contentContainerDesktopInnerItemHeader: {
    gap: spacing(1.5),
    justifyContent: 'flex-end',
  },
  contentContainerDesktopInnerItemLabels: {
    paddingHorizontal: 0,
  },
  contentContainerDesktopInnerItemCurrentPlan: {
    borderRadius: spacing(1),
    backgroundColor: '#F9F9F9',
  },
  contentContainerDesktopInnerItemNewPlan: {
    borderRadius: spacing(1),
    borderWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral200,
  },
  contentContainerInnerMobile: {
    gap: spacing(3),
  },
  contentContainerInnerMobileItem: {
    gap: spacing(1.5),
  },
  contentContainerInnerMobileItemContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: spacing(1),
  },
  disclaimerContainer: {
    marginTop: spacing(4),
    gap: spacing(3),
  },
  disclaimerLink: {
    color: palette.mortarV3.tokenDefault500,
  },
  mobileCurrentPlanContainer: {
    marginTop: spacing(2),
    paddingVertical: 0,
  },
  currentPlanAccordionHeaderItem: {
    marginTop: spacing(1),
    marginBottom: -spacing(1),
  },
  currentPlanAccordion: {
    borderWidth: 0,
    marginHorizontal: -spacing(2),
    width: 'auto',
  },
  currentPlanAccordionContent: {
    paddingHorizontal: spacing(2),
  },
  termsCheckboxContainer: {
    flex: 1,
    flexDirection: 'row',
    gap: spacing(1),
  },
  termsCheckboxText: {
    marginTop: -spacing(0.5),
  },
  upgradeButtonContainerMobile: {
    gap: spacing(1),
    paddingBottom: spacing(2),
  },
  downgradeWarningContainer: {
    paddingHorizontal: spacing(1),
    paddingVertical: spacing(1),
    borderRadius: spacing(0.5),
    borderWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral200,
  },
}));

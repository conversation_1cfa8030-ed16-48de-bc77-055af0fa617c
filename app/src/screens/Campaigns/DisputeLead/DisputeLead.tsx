import React, { useCallback, useState } from 'react';
import {
  createMortarStyles,
  createTestIds,
  spacing,
} from '@cat-home-experts/react-native-utilities';
import { ScrollView, TextInput, TouchableOpacity, View } from 'react-native';
import {
  Button,
  Column,
  Dropdown,
  Icon,
  Loader,
  Row,
  Typography,
} from '@cat-home-experts/react-native-components';
import { useLeadDetails } from 'src/screens/Campaigns/hooks/useLeadDetails';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { AllScreensParamList } from 'src/navigation/routes';
import {
  REPORT_LEAD_SCREEN,
  VIEW_LEAD_DETAILS_SCREEN,
  VIEW_LEADS_SCREEN,
} from 'src/constants';
import { showToast } from 'src/components';
import { PageNotFound } from 'src/screens/PageNotFound';
import { LeadDetail } from 'src/screens/Campaigns/components/LeadDetail';
import { palette as staticPalette } from '@cat-home-experts/react-native-utilities/dist/styles/styles';
import {
  DISPUTE_LEAD,
  DISPUTE_LEAD_REASONS,
} from 'src/screens/Campaigns/constants';
import { LeadDisputeType } from 'src/data/schemas/api/campaigns/CreateLeadDisputeBatchRequest';
import { useMarketplaceJobDetails } from 'src/screens/MarketplaceJobs/hooks/useMarketplaceJobDetails';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { useCampaignDetails } from '../hooks/useCampaignDetails/useCampaignDetails';

const DROPDOWN_OPTIONS = [
  DISPUTE_LEAD_REASONS.SPAM,
  DISPUTE_LEAD_REASONS.OUT_OF_AREA,
  DISPUTE_LEAD_REASONS.WRONG_CATEGORY,
  DISPUTE_LEAD_REASONS.CONTACT_DETAILS,
  DISPUTE_LEAD_REASONS.DUPLICATE,
  DISPUTE_LEAD_REASONS.OTHER,
] as const;
type DropdownOptionsType = (typeof DROPDOWN_OPTIONS)[number];

const dropdownOptionMap: Record<DropdownOptionsType, LeadDisputeType | null> = {
  'Spam (not valid homeowner enquiry)': LeadDisputeType.SPAM,
  'Outside of work area': LeadDisputeType.OUT_OF_AREA,
  'Wrong type of work': LeadDisputeType.WRONG_CATEGORY,
  'Contact details not working': LeadDisputeType.CONTACT_DETAILS,
  Duplicate: LeadDisputeType.DUPLICATE,
  Other: LeadDisputeType.OTHER,
};

const convertOverrideStatusToDropdownValue = (
  status: LeadDisputeType | null,
): DropdownOptionsType => {
  const entry = Object.entries(dropdownOptionMap).find(
    ([_, value]) => value === status,
  );
  return (entry?.[0] as DropdownOptionsType) || 'Other';
};

const convertDropdownValueToOverrideStatus = (
  value: DropdownOptionsType,
): LeadDisputeType | null => {
  return dropdownOptionMap[value];
};

const TEST_IDS = createTestIds('dispute-lead', {});

export const DisputeLead: React.NativeFC<object, typeof TEST_IDS> = () => {
  const { params } =
    useRoute<RouteProp<AllScreensParamList, typeof REPORT_LEAD_SCREEN>>();

  const { leadId, campaignId } = params ?? {};
  if (!leadId || !campaignId) {
    throw new InvalidNavParamsError();
  }

  // Computed Values
  const { lead, loading, disputeLead } = useLeadDetails(leadId);
  const { campaignDetails, isLoading: isCampaignDetailsLoading } =
    useCampaignDetails(campaignId);
  const { job, isLoading: jobLoading } = useMarketplaceJobDetails(
    lead?.jobIdV2,
  );
  const navigation = useNavigation();

  const [leadDisputeType, setLeadDisputeType] = useState<LeadDisputeType>(
    LeadDisputeType.WRONG_CATEGORY,
  );
  const [disputeReason, setDisputeReason] = useState<
    string | null | undefined
  >();
  const [presented, setPresented] = useState<boolean>(false);

  // Methods
  const handleOnChange = useCallback(
    (value: DropdownOptionsType) => {
      const statusValue = convertDropdownValueToOverrideStatus(value);
      if (statusValue) {
        setLeadDisputeType(statusValue);
      }
    },
    [setLeadDisputeType],
  );

  const handleSubmitDispute = useCallback(async () => {
    const success = await disputeLead(leadDisputeType);

    if (success) {
      showToast({
        text1: 'Lead successfully reported',
        type: 'success',
      });

      navigation.navigate(VIEW_LEADS_SCREEN, {
        campaignId,
      });
    } else {
      showToast({
        text1: 'Failed to report lead',
        type: 'error',
      });
    }
  }, [campaignId, disputeLead, leadDisputeType, navigation]);

  const renderTrigger = useCallback(
    () => (
      <TouchableOpacity
        testID={TEST_IDS.TRIGGER}
        style={styles.triggerRoot}
        onPress={() => setPresented(true)}
      >
        <Typography
          useVariant="textLinkSMSemiBold"
          style={styles.triggerText}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {convertOverrideStatusToDropdownValue(leadDisputeType)}
        </Typography>
        <Icon
          name="chevron-down"
          color={staticPalette.mortar.tokenColorBlueGrey}
          size={20}
        />
      </TouchableOpacity>
    ),
    [leadDisputeType],
  );

  if (loading || jobLoading || isCampaignDetailsLoading) {
    return <Loader />;
  }

  if (!lead) {
    return <PageNotFound fallbackScreenToNavigate={VIEW_LEAD_DETAILS_SCREEN} />;
  }

  return (
    <View
      style={[styles.flex, styles.lightBlueBackground]}
      testID={TEST_IDS.ROOT}
    >
      <View style={[styles.flex, styles.mainContentContainer]}>
        <ScrollView>
          <Row style={styles.mainContent}>
            <Column span={4}>
              <LeadDetail
                lead={lead}
                job={job}
                campaignType={campaignDetails?.campaignType}
              />
              <View style={styles.disputeReasonContainer}>
                <Typography useVariant="bodySMSemiBold">
                  {DISPUTE_LEAD.DISPUTE_REASON_LABEL}
                </Typography>
                <Dropdown
                  options={DROPDOWN_OPTIONS as unknown as DropdownOptionsType[]}
                  value={convertOverrideStatusToDropdownValue(leadDisputeType)}
                  onChange={handleOnChange}
                  onChangePresented={(isPresented) => setPresented(isPresented)}
                  keyGetter={(value: string) => value}
                  displayGetter={(value: string) => value}
                  presented={presented}
                  renderTrigger={renderTrigger}
                />
              </View>
              <View style={styles.descriptionContainer}>
                <Typography useVariant="bodySMSemiBold">
                  {DISPUTE_LEAD.DESCRIPTION_LABEL}
                </Typography>
                <TextInput
                  multiline
                  placeholder={DISPUTE_LEAD.DESCRIPTION_PLACEHOLDER}
                  placeholderTextColor="#666666"
                  style={styles.inputStyle}
                  onChangeText={setDisputeReason}
                  value={disputeReason ?? ''}
                />
              </View>
            </Column>
          </Row>
          <View style={styles.buttonContainer}>
            <Button
              block
              label={!lead.isDisputed ? 'Submit' : 'Submitted'}
              onPress={handleSubmitDispute}
              variant="secondary"
              isDisabled={lead.isDisputed}
            />
          </View>
        </ScrollView>
      </View>
    </View>
  );
};

DisputeLead.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette }) => ({
  flex: { flex: 1 },
  lightBlueBackground: {
    backgroundColor: palette.mortar.tokenColorLightBlue,
  },
  mainContentContainer: {
    maxWidth: 800,
    width: '100%',
    alignSelf: 'center',
  },
  mainContent: {
    flex: 1,
    maxWidth: 800,
    width: '100%',
    alignSelf: 'center',
    paddingVertical: spacing(1),
    paddingLeft: spacing(0.5),
    flexDirection: 'row',
    color: palette.mortar.tokenColorBlack,
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    borderTopWidth: 1,
    borderTopColor: palette.system.tokenLegacyColorSurfaceDisabled,
  },
  triggerRoot: {
    minWidth: spacing(16),
    height: spacing(6.25),
    borderRadius: spacing(0.5),
    paddingHorizontal: spacing(1.5),
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: palette.mortar.tokenColorLightGrey,
    gap: spacing(1),
  },
  triggerText: {
    flex: 1,
  },
  inputStyle: {
    paddingVertical: spacing(1.5),
    paddingHorizontal: spacing(1.5),
    minHeight: 100,
    width: '100%',
    borderWidth: 1,
    borderRadius: 5,
    borderColor: palette.mortar.tokenColorLightGrey,
    alignSelf: 'center',
    color: palette.mortar.tokenColorBlack,
  },
  buttonContainer: {
    marginVertical: spacing(1),
    marginHorizontal: spacing(4),
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
  },
  descriptionContainer: {
    paddingVertical: spacing(1),
  },
  disputeReasonContainer: {
    paddingTop: spacing(2),
  },
}));

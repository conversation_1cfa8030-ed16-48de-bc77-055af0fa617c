import React, { ReactElement } from 'react';
import { RouteProp, useRoute } from '@react-navigation/native';
import { Loader } from 'src/components';
import { EDIT_CAMPAIGN_SCREEN } from 'src/constants';
import { AllScreensParamList } from 'src/navigation/routes';
import { useCategories } from 'src/hooks/useCategories';
import { View } from 'react-native';
import { Typography } from '@cat-home-experts/react-native-components';
import { CAMPAIGN_DETAILS } from 'src/screens/Campaigns/constants';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { createTestIds } from 'src/utilities/testIds';
import { useCampaignDetails } from 'src/screens/Campaigns/hooks/useCampaignDetails/useCampaignDetails';
import { CampaignDetailsPages } from 'src/screens/Campaigns/CampaignDetails/pages/CampaignDetailsPages';
import { transformRateBidsToByCategoryId } from 'src/screens/Campaigns/CampaignDetails/transformers/transformRateBidsToByCategoryId';
import { transformGeographiesApiTypeToWorkAreaReducerState } from 'src/screens/Campaigns/CampaignDetails/transformers/transformGeographiesApiTypeToWorkAreaReducerState';
import { captureException } from 'src/services/datadog';
import { getCategoryName } from '../utilities/utils';
import { usePostcodeAreas } from './pages/ReviewAndConfirm/usePostcodeAreas';
import { mapPageNameParamToPageKey } from './utilities/campaignDetailsPages';

const TEST_IDS = createTestIds('existing-campaign-details', {
  ERROR_MESSAGE: 'error-message',
});

export const EditCampaign = (): ReactElement => {
  const { params } =
    useRoute<RouteProp<AllScreensParamList, typeof EDIT_CAMPAIGN_SCREEN>>();
  const {
    postcodeAreas,
    isLoading: isPostcodeAreasLoading,
    hasError: hasPostcodeAreasError,
  } = usePostcodeAreas();
  const {
    categories,
    isLoading: isCategoriesLoading,
    hasError: hasCategoriesError,
  } = useCategories({ fetchAll: true });

  const {
    campaignDetails,
    isLoading: isCampaignDetailsLoading,
    isError: campaignDetailsErrorMessage,
  } = useCampaignDetails(params?.campaignId);
  const campaignDetailsErrorContentStyle = [
    styles.flex,
    styles.errorContainer,
    styles.whiteBackground,
  ];

  // Render error message if there is an error
  if (
    !params?.campaignId ||
    hasCategoriesError ||
    (!isCategoriesLoading && !categories) ||
    hasPostcodeAreasError ||
    (!isPostcodeAreasLoading && !postcodeAreas) ||
    campaignDetailsErrorMessage ||
    (!isCampaignDetailsLoading && !campaignDetails)
  ) {
    captureException(campaignDetailsErrorMessage, {
      Component: 'EditCampaign',
    });
    return (
      <View style={campaignDetailsErrorContentStyle}>
        <Typography use="bodySmall" testID={TEST_IDS.ERROR_MESSAGE}>
          {CAMPAIGN_DETAILS.DATA_ERROR}
        </Typography>
      </View>
    );
  }

  // Render loader if data is loading or images are preloading
  if (
    isCategoriesLoading ||
    isPostcodeAreasLoading ||
    isCampaignDetailsLoading
  ) {
    return <Loader />;
  }

  return (
    <CampaignDetailsPages
      initialState={{
        campaignId: params.campaignId,
        selectedCategoryId: campaignDetails!.category.categoryId,
        selectedCategoryName: getCategoryName(categories, campaignDetails!),
        selectedSubCategoriesIds:
          campaignDetails!.subCategories?.map((cat) => cat.categoryId) ?? [],
        selectedWorkAreas: transformGeographiesApiTypeToWorkAreaReducerState(
          campaignDetails!.geographies || [],
          postcodeAreas,
        ),
        manualBiddingPrices: campaignDetails?.ppl?.rateBids
          ? transformRateBidsToByCategoryId(campaignDetails!.ppl!.rateBids)
          : null,
        isPrimaryCategorySearchable: campaignDetails!.category.isSearchable,
        currentMaxBudget:
          campaignDetails!.currentBudgetAndBalance?.maxSpend ?? 0,
        budgetBoostAmount:
          campaignDetails!.currentBudgetAndBalance?.budgetBoostAmount ?? 0,
        campaignType: campaignDetails!.campaignType,
      }}
      isEditMode={true}
      isConfirmBudgetMode={params.mode === 'confirm-budget'}
      initialPage={mapPageNameParamToPageKey(params.page)}
    />
  );
};

EditCampaign.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  flex: {
    flex: 1,
  },
  errorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing(3),
  },
  whiteBackground: {
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
}));

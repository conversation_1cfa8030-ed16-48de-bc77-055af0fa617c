import React from 'react';
import { Image, View } from 'react-native';
import { Typography } from '@cat-home-experts/react-native-components';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import type { ShareLink } from 'src/data/schemas/api/trade-app-bff/referral/ReferralCampaign';
import { REFERRAL_TEXT } from '../constants';
import { ListSection } from './ListSection';
import { SocialShareLinks } from './SocialShareLinks';
import { CopyAndShareLink } from './CopyAndShareLink';
import { CampaignBottomLinks } from './CampaignBottomLinks';

interface Props {
  campaignId: number;
  referralCode: string;
  referralLink: string;
  socialLinks: ShareLink[];
}

export const CampaignContent: React.FC<Props> = ({
  campaignId,
  referralCode,
  referralLink,
  socialLinks,
}) => (
  <>
    <Image
      source={require('../assets/referral_banner.png')}
      style={styles.image}
      resizeMode="cover"
      alt="Referral banner"
    />
    <View style={styles.contentContainer}>
      <Typography useVariant="headingMDSemiBold" style={styles.sectionMargin}>
        {REFERRAL_TEXT.TITLE}
      </Typography>
      <Typography useVariant="bodyRegular" style={styles.sectionMargin}>
        {REFERRAL_TEXT.DESCRIPTION.A}
      </Typography>
      <Typography useVariant="bodyRegular" style={styles.sectionMargin}>
        {REFERRAL_TEXT.DESCRIPTION.B}
      </Typography>

      <ListSection
        title={REFERRAL_TEXT.WHAT_YOU_EARN.TITLE}
        listItems={REFERRAL_TEXT.WHAT_YOU_EARN.ITEMS}
        footer={REFERRAL_TEXT.WHAT_YOU_EARN.FOOTER}
        style={styles.sectionMargin}
      />
      <ListSection
        title={REFERRAL_TEXT.HOW_DOES_IT_WORK.TITLE}
        listItems={REFERRAL_TEXT.HOW_DOES_IT_WORK.ITEMS}
        footer={REFERRAL_TEXT.HOW_DOES_IT_WORK.FOOTER}
        style={styles.sectionMargin}
      />
      <ListSection
        title={REFERRAL_TEXT.WHAT_DO_THEY_NEED_TO_DO.TITLE}
        listItems={REFERRAL_TEXT.WHAT_DO_THEY_NEED_TO_DO.ITEMS}
        footer={REFERRAL_TEXT.WHAT_DO_THEY_NEED_TO_DO.FOOTER}
        style={styles.mateSection}
      />

      <CopyAndShareLink
        campaignId={campaignId}
        referralCode={referralCode}
        referralLink={referralLink}
        style={styles.sectionMargin}
      />
      <SocialShareLinks socialLinks={socialLinks} style={styles.socialLinks} />

      <CampaignBottomLinks referralLink={referralLink} />
    </View>
  </>
);

const styles = createMortarStyles(({ spacing, palette }) => ({
  image: {
    aspectRatio: 2152 / 506,
    height: undefined,
    width: '100%',
    alignSelf: 'center',
    marginBottom: spacing(1),
  },
  contentContainer: {
    paddingHorizontal: spacing(3),
    paddingTop: spacing(1),
    paddingBottom: spacing(6),
  },
  sectionMargin: {
    marginBottom: spacing(3),
  },
  mateSection: {
    marginBottom: spacing(2),
    marginTop: spacing(3),
  },
  socialLinks: {
    alignSelf: 'center',
    marginBottom: spacing(2),
  },
  errorScreen: {
    flex: 1,
    backgroundColor: palette.mortarV3.tokenNeutral0,
  },
}));

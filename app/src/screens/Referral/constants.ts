export const REFERRAL_TEXT = {
  TITLE: "It's a Lucky Dip – and everyone's a winner.",
  DESCRIPTION: {
    A: "We know trades trust trades. So when you back a mate and help them join Checkatrade, we'll back you both with cold, hard cash.",
    B: "This month only, when your mate signs up, you'll unlock a mystery cash payment. Because solid trades deserve solid rewards.",
  },
  HOW_DOES_IT_WORK: {
    TITLE: 'How it works:',
    ITEMS: [
      '✅  Share your unique referral link',
      '✅  Your mate buys a Fixed Plan or Pay-as-you-go membership',
      '✅  They pass our checks, pay their first month, and you both get your reward through our secure payment platform.',
    ],
    FOOTER:
      "You won't know what you've got until they're in – that's the luck of the draw. But either way, it's a tidy bit of cash for backing a good trade.",
  },
  WHAT_DO_THEY_NEED_TO_DO: {
    TITLE: "To qualify, here's what needs to happen:",
    ITEMS: [
      '•  Your mate must use your referral link (or quote your code on the phone) by the *31st of July 2025*',
      '•  They must pass our checks and pay their first month by the *11th of August 2025*',
      '•  Both of you need a payments account set up by the *11th of August 2025* so we can send you the reward',
    ],
    FOOTER: "This offer won't hang around. Share your link today!",
  },
  WHAT_YOU_EARN: {
    TITLE: 'What could you win?',
    ITEMS: [
      '💰  *£50, £250 or £1,000* when they join on a Fixed Plan',
      '💰  *£50, £75 or £150* if they choose Pay-As-You-Go',
    ],
    FOOTER: '',
  },
  SHARE_MODAL: {
    TITLE: 'Share referral link',
  },
  BOTTOM_LINKS_TEXT: {
    TERMS: 'Terms and Conditions',
    REFER: 'Your referrals',
  },
};

export const CAMPAIGN_NOT_FOUND = {
  TITLE: 'Referral campaign not found',
  DESCRIPTION: 'Please try again later',
};

export const REFERRAL_TERMS_LINK =
  'https://join.checkatrade.com/referral-july2025';

import { TextLink } from '@cat-home-experts/react-native-components';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { FlatList, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { showToast } from 'src/components';
import { ContentSegment } from 'src/components/ContentSegment';
import { Spinner } from 'src/components/primitives/Spinner';
import { FEATURED_PROJECTS_ALL_JOBS_SCREEN, IS_WEB } from 'src/constants';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { ScreenBreakpoints } from 'src/hooks/useMediaQuery/ScreenBreakpoints';
import type { AllScreensParamList } from 'src/navigation/routes';
import { noop } from 'lodash';
import { ProjectCompletedJobSchemaType } from 'src/data/schemas/api/trade-app-bff/project/Project';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { FeaturedProjectRadioCard } from '../FeaturedProjectJobCard';
import { useCompletedJobs } from '../../hooks/useCompletedJobs/useCompletedJobs';
import { FEATURED_PROJECT_ALL_JOBS } from '../../constants';

/**
 * Modal for when 'view all jobs' button is pressed.
 */
export const FeaturedProjectsAllJobs: React.FC = () => {
  const isDesktop = useDesktopMediaQuery();
  const navigation = useNavigation();
  const route =
    useRoute<
      RouteProp<AllScreensParamList, typeof FEATURED_PROJECTS_ALL_JOBS_SCREEN>
    >();
  const { onSelectJob, selectedJobId: initialSelectedJobId } =
    route.params ?? {};
  if (!onSelectJob) {
    throw new InvalidNavParamsError();
  }

  const [selectedJobId, setSelectedJobId] = useState<string>(
    initialSelectedJobId as string,
  );
  const { data: allJobs } = useCompletedJobs();

  // Temporary: This (filter) will eventually need to be completely done on the backend.
  const allJobsNotAssignedToProjects = useMemo(
    () => allJobs?.filter((job) => !job.isAssignedToProject),
    [allJobs],
  );

  const fetchNextPage = noop;
  const isFetchingNextPage = false;
  const isRefetching = false;
  const refetch = noop;

  const handleSave = (jobId: string) => {
    onSelectJob(jobId);
    navigation.goBack();
    showToast({
      text1: FEATURED_PROJECT_ALL_JOBS.SAVED_TOAST_TEXT,
      type: 'success',
    });
  };

  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <TextLink
          style={IS_WEB && styles.saveButton}
          onPress={() => handleSave(selectedJobId)}
          label={FEATURED_PROJECT_ALL_JOBS.SAVE_SELECTION}
        />
      ),
    });
  });

  const renderItem = useCallback(
    ({ item }: { item: ProjectCompletedJobSchemaType }) => {
      const handleSelectJob = () => {
        setSelectedJobId(item.id); // Update local state.
      };

      return (
        <FeaturedProjectRadioCard
          isChecked={selectedJobId === item.id}
          title={item.title}
          date={item.date}
          category={item.category}
          onPress={handleSelectJob}
        />
      );
    },
    [selectedJobId],
  );

  const renderFooter = useCallback(() => {
    if (!isFetchingNextPage) {
      return null;
    }

    return (
      <View style={styles.spinnerContainer}>
        <Spinner size={48} />
      </View>
    );
  }, [isFetchingNextPage]);

  const renderList = useCallback(
    () => (
      <FlatList
        data={allJobsNotAssignedToProjects}
        renderItem={renderItem}
        ListFooterComponent={renderFooter}
        onEndReachedThreshold={0.2}
        onEndReached={fetchNextPage}
        refreshing={isRefetching}
        onRefresh={refetch}
        contentContainerStyle={styles.container}
      />
    ),
    [
      allJobsNotAssignedToProjects,
      fetchNextPage,
      isRefetching,
      refetch,
      renderFooter,
      renderItem,
    ],
  );

  const renderMobileContent = useCallback(() => {
    return renderList();
  }, [renderList]);

  const renderDesktopContent = useCallback(() => {
    return (
      <ScrollView contentContainerStyle={styles.containerDesktop}>
        <ContentSegment>{renderList()}</ContentSegment>
      </ScrollView>
    );
  }, [renderList]);

  return isDesktop ? renderDesktopContent() : renderMobileContent();
};

const styles = createMortarStyles(({ spacing }) => ({
  spinnerContainer: {
    alignItems: 'center',
    marginVertical: spacing(4),
  },
  container: {
    padding: spacing(2),
    gap: spacing(1),
  },
  containerDesktop: {
    paddingVertical: spacing(2),
    maxWidth: ScreenBreakpoints.Medium,
    marginHorizontal: 'auto',
    width: '100%',
  },
  saveButton: {
    paddingRight: spacing(2),
  },
}));

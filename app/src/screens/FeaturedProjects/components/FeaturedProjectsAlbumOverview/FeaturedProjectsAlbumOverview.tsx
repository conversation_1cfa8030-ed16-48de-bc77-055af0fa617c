import { tokenColorPrimaryWhite } from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import { Typography } from '@cat-home-experts/react-native-components';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { noop } from 'lodash';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { Loader, showToast } from 'src/components';
import { ALBUM_SCREEN } from 'src/constants';
import type { ImageType } from 'src/data/schemas/firestore/companies/albums';
import type { AllScreensParamList } from 'src/navigation/routes';
import { ImageView } from 'src/screens/Photos/components/ImageView';
import { usePhotoAlbums } from 'src/screens/Photos/usePhotoAlbums';
import { sortAccordingToOrderArray } from 'src/utilities/photos';
import { getTestID } from 'src/utilities/testIds';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import {
  FEATURED_PROJECT_ALBUM,
  FEATURED_PROJECT_STAGES,
} from '../../constants';
import { AlbumSelectionHeader as FeaturedProjectsAlbumSelectionHeader } from './AlbumSelectionHeader';
import { AlbumSelectionImageList as FeaturedProjectsAlbumSelectionImageList } from './AlbumSelectionImageList';
import { useFeaturedProjectsDraft } from '../../hooks/useFeaturedProjectsDraft';

const rootTestId = 'album-overview';

export const testIds = {
  ROOT: rootTestId,
};

export const FeaturedProjectsAlbumOverview = (): React.ReactElement => {
  const navigation = useNavigation();

  const route = useRoute<RouteProp<AllScreensParamList, typeof ALBUM_SCREEN>>();
  const { albumId } = route.params ?? {};
  if (!albumId) {
    throw new InvalidNavParamsError();
  }

  const { albumData, isLoading } = usePhotoAlbums(albumId);

  const [pressedImageIndex, setPressedImageIndex] = useState<number>();
  const [selectedImages, setSelectedImages] = useState<ImageType[]>([]);

  const { updateStage, draftState } = useFeaturedProjectsDraft();

  const addPhotosDraftState =
    draftState?.stages?.[FEATURED_PROJECT_STAGES.ADD_PHOTOS];

  const publishSelectedImageIdsFromDraftState = useCallback(() => {
    if (!addPhotosDraftState) {
      setSelectedImages([]);
      return;
    }

    setSelectedImages(addPhotosDraftState.selectedImages);
  }, [addPhotosDraftState]);

  useEffect(() => {
    publishSelectedImageIdsFromDraftState();
  }, [publishSelectedImageIdsFromDraftState]);

  const saveSelectedImages = async () => {
    updateStage(FEATURED_PROJECT_STAGES.ADD_PHOTOS, {
      ...addPhotosDraftState, // Spread previous state.
      selectedImages: selectedImages,
    });

    navigation.goBack();

    showToast({
      text1: 'Images saved',
      type: 'success',
    });
  };

  const sortedAlbumItems = useMemo(
    () =>
      albumData
        ? sortAccordingToOrderArray<ImageType>(
            albumData.items,
            albumData.itemsOrder,
          )
        : [],
    [albumData],
  );

  useEffect(() => {
    if (albumData?.items.length === 0) {
      setPressedImageIndex(undefined);
    }
  }, [albumData?.items]);

  if (isLoading) {
    return <Loader />;
  }

  return (
    <View style={styles.container} testID={getTestID(testIds.ROOT)}>
      {albumData?.items ? (
        <>
          <FeaturedProjectsAlbumSelectionHeader
            selectedImages={selectedImages}
            selectedImagesFromDraftState={addPhotosDraftState?.selectedImages}
            onPressDone={saveSelectedImages}
            onPressBack={() => navigation.goBack()}
          />
          <FeaturedProjectsAlbumSelectionImageList
            state={{
              selectedImages,
              setSelectedImages,
            }}
            images={sortedAlbumItems}
            albumData={albumData}
            isSelectMode
            onMenuClose={noop}
            onImagePress={(index) => setPressedImageIndex(index)}
          />
          {pressedImageIndex != null ? (
            <ImageView
              albumId={albumData.id}
              images={sortedAlbumItems}
              initialImageIndex={pressedImageIndex}
              onClose={() => setPressedImageIndex(undefined)}
            />
          ) : null}
        </>
      ) : (
        <Typography use="bodyRegular">
          {FEATURED_PROJECT_ALBUM.NO_ALBUM_DETAILS}
        </Typography>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: tokenColorPrimaryWhite,
  },
});

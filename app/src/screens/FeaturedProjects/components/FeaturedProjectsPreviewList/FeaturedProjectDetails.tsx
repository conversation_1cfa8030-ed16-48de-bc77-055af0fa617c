import React, { useEffect, useState } from 'react';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import {
  Icon,
  Spinner,
  Typography,
} from '@cat-home-experts/react-native-components';
import { AllScreensParamList } from 'src/navigation/routes';
import {
  FEATURED_PROJECT_DETAILS,
  IS_WEB,
  PROFILE_SCREEN,
} from 'src/constants';
import { ContentSegment } from 'src/components/ContentSegment';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { ScreenBreakpoints } from 'src/hooks/useMediaQuery/ScreenBreakpoints';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import { DeleteConfirmation } from 'src/components/DeleteConfirmationModal/DeleteConfirmation';
import { InformationCircleFill } from '@cat-home-experts/mortar-iconography-native';
import { tokenAttention500 } from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV3';
import { useUserContext } from 'src/hooks/useUser';
import { PageNotFound } from 'src/screens/PageNotFound';
import { showToast } from 'src/components';
import { usePhotoAlbums } from 'src/screens/Photos/usePhotoAlbums';
import { useMarketplaceJobDetails } from 'src/screens/MarketplaceJobs/hooks';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { useQueryClient } from '@tanstack/react-query';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { PublishSummaryCard } from '../PublishSummaryCard';
import { useFeaturedProjectDetails } from '../../hooks/useFeaturedProjectDetails';
import { useFeaturedProjects } from '../../hooks/useFeaturedProjects';
import {
  FEATURED_PROJECT_DETAILS_DELETE_MODAL,
  FEATURED_PROJECT_UNTITLED_PROJECT,
} from '../../constants';
import { QUERY_KEY_FEATURED_PROJECTS_COMPLETED_JOBS } from '../../hooks/useCompletedJobs/constants';

const TEST_IDS = createTestIds('featured-project-details', {
  DELETE_BUTTON: 'delete-button',
});

export const FeaturedProjectDetails: React.FC = () => {
  const queryClient = useQueryClient();
  const { companyId } = useUserContext();
  const navigation = useNavigation();
  const isDesktop = useDesktopMediaQuery();

  const route =
    useRoute<RouteProp<AllScreensParamList, typeof FEATURED_PROJECT_DETAILS>>();
  const { featuredProjectId } = route.params ?? {};
  if (!featuredProjectId) {
    throw new InvalidNavParamsError();
  }

  const { data: featuredProject, error: fetchError } =
    useFeaturedProjectDetails(featuredProjectId);

  const { albumData } = usePhotoAlbums(featuredProject?.albumId || undefined);
  const { job } = useMarketplaceJobDetails(featuredProject?.jobId);

  const { remove, removeFetching } = useFeaturedProjects();

  const [showDeletionConfirmation, setShowDeletionConfirmation] =
    useState(false);

  useEffect(() => {
    navigation.setOptions({
      headerTitle: 'Featured Projects',
      headerBackTitle: 'Back',
    });
  }, [navigation]);

  const renderHeader = () => {
    return (
      <View style={styles.header}>
        <Typography useVariant="bodySemiBold">
          {featuredProject?.projectName}
        </Typography>
        <TouchableOpacity
          testID={TEST_IDS.DELETE_BUTTON}
          disabled={removeFetching}
          onPress={() => {
            setShowDeletionConfirmation(true);
            logEvent(EVENT_TYPE.FEATURED_PROJ_DELETE_CLICKED, {
              featuredProjectId: featuredProjectId,
            });
          }}
        >
          <Icon name="bin" size={20} />
        </TouchableOpacity>
      </View>
    );
  };

  const handleDelete = async () => {
    await remove(featuredProjectId);
    showToast({
      text1: 'Featured Project deleted',
      type: 'success',
    });

    setShowDeletionConfirmation(false);
    await queryClient.invalidateQueries({
      queryKey: [QUERY_KEY_FEATURED_PROJECTS_COMPLETED_JOBS],
    });

    if (IS_WEB) {
      navigation.navigate(PROFILE_SCREEN);
    } else {
      navigation.goBack();
    }
  };

  if (fetchError) {
    return <PageNotFound fallbackScreenToNavigate="Profile" />;
  }

  if (!featuredProject) {
    return (
      <View style={styles.spinnerContainer}>
        <View style={styles.spinner}>
          <Spinner size={48} />
        </View>
      </View>
    );
  }

  return (
    <ScrollView style={isDesktop ? styles.containerDesktop : styles.container}>
      <SafeAreaView edges={['bottom']}>
        <DeleteConfirmation
          icon={
            <InformationCircleFill
              size={isDesktop ? 48 : 32}
              color={tokenAttention500}
            />
          }
          isVisible={showDeletionConfirmation}
          header={FEATURED_PROJECT_DETAILS_DELETE_MODAL.HEADER}
          confirmationText={
            FEATURED_PROJECT_DETAILS_DELETE_MODAL.CONFIRMATION_TEXT
          }
          onDelete={handleDelete}
          onClose={() => setShowDeletionConfirmation(false)}
        />
        <ContentSegment style={isDesktop && styles.contentDesktop}>
          {renderHeader()}
          {companyId && job && (
            <PublishSummaryCard
              projectName={
                featuredProject?.projectName ||
                FEATURED_PROJECT_UNTITLED_PROJECT
              }
              companyId={companyId}
              cost={featuredProject?.cost || 0}
              jobId={job.id}
              job={job}
              startDate={
                featuredProject?.startDate
                  ? new Date(featuredProject.startDate)
                  : new Date()
              }
              endDate={
                featuredProject?.endDate
                  ? new Date(featuredProject.endDate)
                  : new Date()
              }
              images={albumData ? albumData.items : []}
              selectedReviewData={{
                type: 'select',
                id:
                  featuredProject.reviewId !== null
                    ? featuredProject.reviewId
                    : undefined,
              }}
            />
          )}
        </ContentSegment>
      </SafeAreaView>
    </ScrollView>
  );
};

FeaturedProjectDetails.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing }) => ({
  container: {
    flex: 1,
  },
  containerDesktop: {
    flex: 1,
    paddingVertical: spacing(4),
  },
  contentDesktop: {
    maxWidth: ScreenBreakpoints.Medium,
    marginHorizontal: 'auto',
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing(2),
    flex: 1,
  },
  spinner: {
    width: 48,
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
  },
  spinnerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
}));

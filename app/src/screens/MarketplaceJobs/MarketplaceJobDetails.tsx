import React from 'react';
import { type RouteProp, useRoute } from '@react-navigation/native';
import { isTruthy } from '@cat-home-experts/react-native-utilities';
import { isAxiosError } from 'axios';
import { JOBS_SCREEN, MARKETPLACE_JOB_DETAILS_SCREEN } from 'src/constants';
import type { AllScreensParamList } from 'src/navigation/routes';
import { PageNotFound } from 'src/screens/PageNotFound';
import { useArchivedJobDetails } from 'src/screens/Jobs/hooks/useArchivedJobDetails';
import { JobDetailsContent } from 'src/screens/Jobs/containers/JobDetailsContent';
import { NetworkErrorView } from 'src/components/NetworkErrorView';
import { Loader } from 'src/components';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { useMarketplaceJobDetails } from './hooks/useMarketplaceJobDetails';
import { JobDetailsV2 } from '../JobDetailsV2';

type CurrentRoute = RouteProp<
  AllScreensParamList,
  typeof MARKETPLACE_JOB_DETAILS_SCREEN
>;

export const MarketplaceJobDetails: React.FC = () => {
  // Navigation
  const route = useRoute<CurrentRoute>();
  const { id, isArchivedJob = false } = route.params ?? {};
  if (!id) {
    throw new InvalidNavParamsError();
  }

  // Computed Values
  const { job, isLoading, jobError, refetchJob, ...methods } =
    useMarketplaceJobDetails(id, !isArchivedJob);

  const isArchivedJobHookEnabled = isArchivedJob || (!isLoading && !job);
  const { archivedJob, isLoading: isArchivedJobLoading } =
    useArchivedJobDetails(id, isArchivedJobHookEnabled);

  if (isLoading) {
    return <Loader />;
  }

  if (!job) {
    if (isArchivedJobLoading) {
      return <Loader />;
    }

    // This logic is mainly to handle deep linking to either an old job
    // or an old job id which has a chat job
    if (isTruthy(archivedJob)) {
      return <JobDetailsContent job={archivedJob} />;
    }

    if (isAxiosError(jobError) && jobError.response?.status === 404) {
      return <PageNotFound fallbackScreenToNavigate={JOBS_SCREEN} />;
    } else {
      return (
        <NetworkErrorView
          dataName="Job"
          error={jobError}
          dataRefresh={refetchJob}
        />
      );
    }
  }

  return <JobDetailsV2 job={job} {...methods} />;
};

import React, { ReactElement, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { tokenColorPrimaryWhite } from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import { getTestID } from 'src/utilities/testIds';
import { Button, TextArea } from '@cat-home-experts/react-native-components';
import { showToast } from 'src/components';
import { RouteProp, useRoute } from '@react-navigation/native';
import { EDIT_ALBUM_SCREEN } from 'src/constants';
import {
  ALBUM_DESCRIPTION_MAX_LENGTH,
  ALBUM_TITLE_MAX_LENGTH,
  PHOTOS_CONTAINER_MAX_WIDTH_STYLES,
} from 'src/screens/Photos/constants';
import type { AllScreensParamList } from 'src/navigation/routes';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { usePhotoAlbums } from './usePhotoAlbums';

const rootTestId = 'edit-album-screen';

export const testIds = {
  ROOT: rootTestId,
  TITLE_INPUT: `${rootTestId}-title-input`,
  DESCRIPTION_INPUT: `${rootTestId}-description-input`,
  SAVE: `${rootTestId}-save`,
};

export const EditAlbum = (): ReactElement => {
  const route =
    useRoute<RouteProp<AllScreensParamList, typeof EDIT_ALBUM_SCREEN>>();
  const { title, albumId, description } = route.params ?? {};
  if (!title || !albumId || !description) {
    throw new InvalidNavParamsError();
  }

  const { editPhotoAlbum } = usePhotoAlbums();
  const [isRequestLoading, setIsRequestLoading] = useState(false);
  const [albumTitle, setAlbumTitle] = useState(title);
  const [albumDescription, setAlbumDescription] = useState(description);

  const isFormValid = albumTitle !== '' && albumDescription !== '';

  const handleEditAlbum = async () => {
    try {
      setIsRequestLoading(true);
      await editPhotoAlbum({
        id: albumId,
        title: albumTitle,
        description: albumDescription,
      });
      showToast({
        type: 'success',
        text1: 'Album details edited',
      });
    } catch {
      showToast({
        type: 'error',
        text1: 'Could not save',
      });
    } finally {
      setIsRequestLoading(false);
    }
  };

  return (
    <View style={styles.wrapper}>
      <View style={styles.container} testID={getTestID(testIds.ROOT)}>
        <TextArea
          label="Album title"
          onChangeText={setAlbumTitle}
          value={albumTitle}
          maxLength={ALBUM_TITLE_MAX_LENGTH}
          isDisabled={isRequestLoading}
          hideFocusBorder
          keyboardType="default"
          containerStyle={styles.titleWrapper}
          placeholder="Album title"
          testID={getTestID(testIds.TITLE_INPUT)}
        />
        <TextArea
          label="Enter description"
          onChangeText={setAlbumDescription}
          value={albumDescription}
          maxLength={ALBUM_DESCRIPTION_MAX_LENGTH}
          multiline
          isDisabled={isRequestLoading}
          hideLabel
          hideFocusBorder
          keyboardType="default"
          containerStyle={styles.descriptionWrapper}
          textInputStyle={styles.descriptionTextArea}
          placeholder="Enter description"
          testID={getTestID(testIds.DESCRIPTION_INPUT)}
        />
        <Button
          style={styles.button}
          label="Save"
          size="small"
          block
          onPress={handleEditAlbum}
          variant="secondary"
          isDisabled={!isFormValid || isRequestLoading}
          testID={testIds.SAVE}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: tokenColorPrimaryWhite,
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 24,
    backgroundColor: tokenColorPrimaryWhite,
    ...PHOTOS_CONTAINER_MAX_WIDTH_STYLES,
  },
  button: {
    marginTop: 24,
  },
  titleWrapper: {
    marginBottom: 16,
  },
  descriptionWrapper: {
    height: 120,
  },
  descriptionTextArea: {
    height: 115,
  },
});

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Loader, showToast } from 'src/components';
import { StyleSheet, View } from 'react-native';
import { getTestID } from 'src/utilities/testIds';
import { tokenColorPrimaryWhite } from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { ALBUM_SCREEN } from 'src/constants';
import { usePhotoAlbums } from 'src/screens/Photos/usePhotoAlbums';
import { usePhotos } from 'src/screens/Photos/usePhotos';
import { AlbumOverviewHeader } from 'src/screens/Photos/components/AlbumOverviewHeader';
import { AlbumImageList } from 'src/screens/Photos/components/AlbumImageList';
import { Typography } from '@cat-home-experts/react-native-components';
import { AlbumOverviewMenu } from 'src/screens/Photos/components/AlbumOverviewMenu';
import { SortableImageList } from 'src/screens/Photos/components/SortableImageList';
import { ImageView } from 'src/screens/Photos/components/ImageView';
import { sortAccordingToOrderArray } from 'src/utilities/photos';
import type { AllScreensParamList } from 'src/navigation/routes';
import type { ImageType } from 'src/data/schemas/firestore/companies/albums';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { FeaturedImagesSelect } from './components/FeaturedImagesSelect';
import type { ViewMode } from './photos.types';

const rootTestId = 'album-overview';

export const testIds = {
  ROOT: rootTestId,
};

export const AlbumOverview = (): React.ReactElement => {
  const navigation = useNavigation();
  const route = useRoute<RouteProp<AllScreensParamList, typeof ALBUM_SCREEN>>();
  const { albumId } = route.params ?? {};
  if (!albumId) {
    throw new InvalidNavParamsError();
  }

  const { albumData, isLoading } = usePhotoAlbums(albumId);
  const { savePhotoOrder } = usePhotos(albumId);
  const [isSavingPhotoOrder, setIsSavingPhotoOrder] = useState(false);
  const [disableFeaturedPhotosDoneButton, setDisableFeaturedPhotosDoneButton] =
    useState(true);

  const [showTitleInHeader, setShowTitleInHeader] = useState(false);
  const [pressedImageIndex, setPressedImageIndex] = useState<number>();

  const [viewMode, setViewMode] = useState<ViewMode>('default');
  const sortedAlbumItems = useMemo(
    () =>
      albumData
        ? sortAccordingToOrderArray<ImageType>(
            albumData.items,
            albumData.itemsOrder,
          )
        : [],
    [albumData],
  );
  const [pendingItemsOrder, setPendingItemsOrder] = useState<ImageType[]>();
  const [pendingFeaturedPhotos, setPendingFeaturedPhotos] =
    useState<ImageType[]>();

  useEffect(() => {
    if (albumData?.items.length === 0) {
      setPressedImageIndex(undefined);
    }
  }, [albumData?.items]);

  const handleDoneChangingOrder = useCallback(async () => {
    try {
      setIsSavingPhotoOrder(true);
      if (!pendingItemsOrder || pendingItemsOrder.length <= 0) {
        throw new Error('Error item order not set');
      }

      await savePhotoOrder(pendingItemsOrder);
      showToast({
        type: 'success',
        text1: 'Order saved',
      });
    } catch {
      showToast({
        type: 'error',
        text1: 'Unable to save order. Please retry',
      });
    } finally {
      setPendingItemsOrder(undefined);
      setIsSavingPhotoOrder(false);
      setViewMode('default');
    }
  }, [pendingItemsOrder, savePhotoOrder]);

  useEffect(() => {
    if (
      pendingFeaturedPhotos?.length === 3 ||
      pendingFeaturedPhotos?.length === sortedAlbumItems.length
    ) {
      setDisableFeaturedPhotosDoneButton(false);
    } else {
      setDisableFeaturedPhotosDoneButton(true);
    }
  }, [pendingFeaturedPhotos, sortedAlbumItems.length]);

  const handleSetFeaturedImages = useCallback(async () => {
    try {
      const filteredItems = sortedAlbumItems.filter(
        (image) =>
          !pendingFeaturedPhotos?.find(
            (featuredPhoto) => featuredPhoto.id === image.id,
          ),
      );

      const newSortedPhotos = [
        ...(pendingFeaturedPhotos as ImageType[]),
        ...filteredItems,
      ];

      setIsSavingPhotoOrder(true);
      if (!pendingFeaturedPhotos || pendingFeaturedPhotos.length <= 0) {
        throw new Error('Error: featured item order not set');
      }

      await savePhotoOrder(newSortedPhotos);

      showToast({
        type: 'success',
        text1: 'Featured photos saved',
      });
    } catch {
      showToast({
        type: 'error',
        text1: 'Unable to save featured photos. Please retry',
      });
    } finally {
      setPendingFeaturedPhotos(undefined);
      setIsSavingPhotoOrder(false);
      setViewMode('default');
    }
  }, [pendingFeaturedPhotos, savePhotoOrder, sortedAlbumItems]);

  if (isLoading) {
    return <Loader />;
  }

  return (
    <View style={styles.container} testID={getTestID(testIds.ROOT)}>
      {albumData?.items ? (
        <>
          <AlbumOverviewHeader
            title={albumData.title}
            showTitleInHeader={showTitleInHeader}
            viewMode={viewMode}
            onPressBack={() => {
              if (viewMode !== 'default') {
                setViewMode('default');
              } else {
                navigation.goBack();
              }
            }}
            onMenuOpen={() => setViewMode('album-overview-menu')}
            onSelectModeDone={() => setViewMode('default')}
            onDoneChangingOrder={handleDoneChangingOrder}
            disableRightButton={disableFeaturedPhotosDoneButton}
            onDoneSelectingFeaturedImages={handleSetFeaturedImages}
          />
          {viewMode !== 'order' && (
            <AlbumImageList
              images={sortedAlbumItems}
              albumData={albumData}
              isRequestLoading={isSavingPhotoOrder}
              isSelectMode={viewMode === 'select'}
              onToggleHeaderTitleVisibility={setShowTitleInHeader}
              onMenuClose={() => setViewMode('default')}
              onImagePress={(index) => setPressedImageIndex(index)}
            />
          )}
          {viewMode === 'order' &&
            (isSavingPhotoOrder ? (
              <Loader />
            ) : (
              <SortableImageList
                items={pendingItemsOrder || sortedAlbumItems}
                setItemsOrder={setPendingItemsOrder}
              />
            ))}
          {viewMode === 'featured-images-select' && (
            <FeaturedImagesSelect
              items={sortedAlbumItems}
              setPendingFeaturedPhotos={setPendingFeaturedPhotos}
            />
          )}
          <AlbumOverviewMenu
            onMenuClose={() => setViewMode('default')}
            onSelectPhotos={() => setViewMode('select')}
            onChangeOrder={() => setViewMode('order')}
            isVisible={viewMode === 'album-overview-menu'}
            album={albumData}
          />
          {pressedImageIndex != null ? (
            <ImageView
              albumId={albumData.id}
              images={sortedAlbumItems}
              initialImageIndex={pressedImageIndex}
              onClose={() => setPressedImageIndex(undefined)}
            />
          ) : null}
        </>
      ) : (
        <Typography use="bodyRegular">{'No album details'}</Typography>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: tokenColorPrimaryWhite,
  },
});

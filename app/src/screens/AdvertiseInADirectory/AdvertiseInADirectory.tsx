import React, { ReactElement, useEffect } from 'react';
import { View, ScrollView, FlatList } from 'react-native';

import { Button, Typography } from '@cat-home-experts/react-native-components';
import type { NewTypographyVariants } from '@cat-home-experts/react-native-components';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import DirectoriesBanner from 'src/assets/images/directories/directories-banner.png';
import { createTestIds, getTestID } from 'src/utilities/testIds';
import { TEL_CROSS_SALES, DIRECTORIES_CALLBACK_FORM_URL } from 'src/constants';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE, ANALYTICS_ACTION_TYPE } from 'src/constants.events';

import { openExternalLink } from 'src/utilities/openExternalLink';
import { AspectRatioImage } from 'src/components/primitives/AspectRatioImage';
import { useMobileMediaQuery } from 'src/hooks/useMediaQuery';
import {
  ContactType,
  formatPhoneNumber,
  handlePhoneContact,
} from 'src/utilities/phone';
import { AD_TYPES, type AdType } from './constants';

const screen = 'advertise_directory';

const TEST_IDS = createTestIds('advertise-directory', {
  ROOT: 'root',
  HERO_IMAGE: 'hero-image',
  CALL_US_BUTTON: 'call-us-button',
  CALL_US_TEXT: 'call-us-text',
  CALLBACK_REQUEST_BUTTON: 'callback-request-button',
  AD_CAROUSEL: 'ad-carousel',
  AD_GRID: 'ad-grid',
  AD_CARD: 'ad-card',
});

// Hero Section Component
const HeroSection = ({ isMobile }: { isMobile: boolean }) => {
  return (
    <View style={styles.heroContainer}>
      <AspectRatioImage
        testID={getTestID(TEST_IDS.HERO_IMAGE)}
        style={styles.heroImage}
        source={DirectoriesBanner} // Using placeholder for now
        dimensionToCalculate="height"
        aspectRatio={isMobile ? 327 / 400 : 550 / 200} // Different aspect ratios for mobile vs web
        resizeMode="cover"
        accessible
        accessibilityLabel="Advertise in a directory hero image"
        accessibilityRole="image"
      />
    </View>
  );
};

// Description Block Component
const DescriptionBlock = ({
  isMobile,
  onCallUs,
  onRequestCallback,
}: {
  isMobile: boolean;
  onCallUs: () => void;
  onRequestCallback: () => void;
}) => {
  return (
    <View style={styles.descriptionContainer}>
      <Typography use="subHeader" style={styles.title}>
        {'Advertise in a directory'}
      </Typography>
      <Typography use="bodyRegular" style={styles.description}>
        {'Our Checkatrade directories can help you to grow your '}
        {'business by putting your trade in front of a '}
        {'whole new audience in the areas you want to work.'}
      </Typography>

      {isMobile ? (
        <View style={styles.mobileButtonContainer}>
          <Button
            block
            variant="secondary"
            label="Call us"
            onPress={onCallUs}
            testID={getTestID(TEST_IDS.CALL_US_BUTTON)}
            style={styles.buttonSpacing}
          />
          <Button
            block
            variant="tertiary"
            label="Book a call back"
            onPress={onRequestCallback}
            testID={getTestID(TEST_IDS.CALLBACK_REQUEST_BUTTON)}
          />
        </View>
      ) : (
        <View style={styles.webButtonContainer}>
          <Typography
            useVariant="subHeadingRegular"
            testID={getTestID(TEST_IDS.CALL_US_TEXT)}
          >
            {'Call us on '}
            <Typography useVariant="subHeadingSemiBold">
              {` ${formatPhoneNumber(TEL_CROSS_SALES)}`}
            </Typography>
            {'  or  '}
          </Typography>
          <Button
            variant="tertiary"
            label="Book a call back"
            onPress={onRequestCallback}
            testID={getTestID(TEST_IDS.CALLBACK_REQUEST_BUTTON)}
          />
        </View>
      )}
    </View>
  );
};

// Marketing Details Section Component
const MarketingDetailsSection = () => {
  const bulletList = [
    [
      { value: 'With ' },
      {
        value: '26 years of service',
        typographyVariant: 'bodyBold' as NewTypographyVariants,
      },
      {
        value:
          ', these directories cover 358 local areas, reaching an average of 70,000 households per area.',
      },
    ],
    [
      { value: 'They are circulated bi-monthly, with ' },
      {
        value: '23.6 million copies ',
        typographyVariant: 'bodyBold' as NewTypographyVariants,
      },
      { value: 'distributed per drop.' },
    ],
    [
      { value: 'With an ' },
      {
        value: '80% readership ',
        typographyVariant: 'bodyBold' as NewTypographyVariants,
      },
      { value: 'among the households they reach, they have a ' },
      {
        value: '40% longer shelf life ',
        typographyVariant: 'bodyBold' as NewTypographyVariants,
      },
      {
        value:
          'in homes compared to other forms of print, demonstrating their lasting value and trusted reputation.',
      },
    ],
  ];

  return (
    <View style={styles.marketingContainer}>
      <View style={styles.marketingSection}>
        <Typography use="subHeader" style={styles.sectionTitle}>
          {'Get more leads'}
        </Typography>
        <Typography use="bodyRegular" style={styles.sectionText}>
          {'Directories are an effective way of reaching a valuable '}
          {'audience, with the product delivered directly through a '}
          {"customers' letterbox."}
        </Typography>
        <Typography use="bodyRegular" style={styles.sectionTextLast}>
          {'Directories not only drive immediate responses but also '}
          {'create lasting halo effects, enhancing brand awareness and '}
          {'customer retention.'}
        </Typography>
      </View>

      <View style={styles.marketingSection}>
        <Typography use="subHeader" style={styles.sectionTitle}>
          {'Beat the competition'}
        </Typography>
        <Typography use="bodyRegular" style={styles.sectionText}>
          {'We have a limit for each trade heading, which means '}
          {"you'll be competing with fewer trades within the area."}
        </Typography>
        <Typography use="bodyRegular" style={styles.sectionTextLast}>
          {'Select for our range of advertisement options to showcase '}
          {'your business and be top of mind when a customer needs you.'}
        </Typography>
      </View>

      <View style={styles.marketingSection}>
        <Typography use="subHeader" style={styles.sectionTitle}>
          {'26 years of success'}
        </Typography>
        <BulletList list={bulletList} />
      </View>
    </View>
  );
};

// Ad Type Card Component
const AdTypeCard = ({ adType }: { adType: AdType }) => {
  return (
    <View style={styles.adCard}>
      <AspectRatioImage
        style={styles.adCardImage}
        source={adType.image}
        dimensionToCalculate="height"
        aspectRatio={1}
        resizeMode="cover"
        accessible
        accessibilityLabel={`${adType.title} advertisement example`}
        accessibilityRole="image"
      />
      <View style={styles.adCardContent}>
        <Typography use="subHeader" style={styles.adCardTitle}>
          {adType.title}
        </Typography>
        <Typography use="bodyRegular" style={styles.adCardDescription}>
          {adType.description}
        </Typography>
      </View>
    </View>
  );
};

// Ad Types Carousel Component (Mobile)
const AdTypesCarousel = () => {
  const renderAdType = ({ item }: { item: AdType }) => (
    <AdTypeCard adType={item} />
  );

  return (
    <View style={styles.carouselContainer}>
      <Typography use="subHeader" style={styles.sectionTitle}>
        {'Choose the right advert for your business needs'}
      </Typography>
      <FlatList
        testID={getTestID(TEST_IDS.AD_CAROUSEL)}
        data={AD_TYPES}
        renderItem={renderAdType}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.carouselContent}
        ItemSeparatorComponent={() => <View style={styles.carouselSeparator} />}
      />
    </View>
  );
};

// Ad Types Grid Component (Web)
const AdTypesGrid = () => {
  const renderAdType = ({ item }: { item: AdType }) => (
    <View style={styles.gridItem}>
      <AdTypeCard adType={item} />
    </View>
  );

  return (
    <View style={styles.gridContainer}>
      <Typography use="subHeader" style={styles.sectionTitle}>
        {'Choose the right advert for your business needs'}
      </Typography>
      <FlatList
        testID={getTestID(TEST_IDS.AD_GRID)}
        data={AD_TYPES}
        renderItem={renderAdType}
        keyExtractor={(item) => item.id}
        numColumns={2}
        contentContainerStyle={styles.gridContent}
        columnWrapperStyle={styles.gridRow}
      />
    </View>
  );
};

export function AdvertiseInADirectory(): ReactElement {
  const isMobile = useMobileMediaQuery();

  const handleCallUs = () => {
    logEvent(EVENT_TYPE.DIRECTORIES_CALL_US);
    handlePhoneContact(ContactType.call, TEL_CROSS_SALES);
  };

  const handleRequestCallback = () => {
    logEvent(EVENT_TYPE.DIRECTORIES_REQUEST_CALLBACK);
    openExternalLink(DIRECTORIES_CALLBACK_FORM_URL);
  };

  useEffect(() => {
    logEvent(`${screen}_${ANALYTICS_ACTION_TYPE.VIEWED}`);
  }, []);

  return (
    <ScrollView style={styles.scrollView}>
      <View testID={getTestID(TEST_IDS.ROOT)} style={styles.container}>
        {/* Hero Section */}
        <HeroSection isMobile={isMobile} />

        {/* Description Block */}
        <DescriptionBlock
          isMobile={isMobile}
          onCallUs={handleCallUs}
          onRequestCallback={handleRequestCallback}
        />

        {/* Marketing Details */}
        <MarketingDetailsSection />

        {/* Ad Types - Carousel for Mobile, Grid for Web */}
        {isMobile ? <AdTypesCarousel /> : <AdTypesGrid />}
      </View>
    </ScrollView>
  );
}

AdvertiseInADirectory.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  scrollView: {
    flex: 1,
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  container: {
    flex: 1,
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },

  // Hero Section Styles
  heroContainer: {
    width: '100%',
  },
  heroImage: {
    width: '100%',
  },

  // Description Block Styles
  descriptionContainer: {
    padding: spacing(3),
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  title: {
    marginBottom: spacing(2),
    textAlign: 'center',
  },
  description: {
    marginBottom: spacing(3),
    textAlign: 'center',
    lineHeight: 24,
  },
  mobileButtonContainer: {
    gap: spacing(2),
  },
  webButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing(1),
  },
  buttonSpacing: {
    marginBottom: spacing(2),
  },

  // Marketing Section Styles
  marketingContainer: {
    padding: spacing(3),
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  marketingSection: {
    marginBottom: spacing(4),
  },
  sectionTitle: {
    marginBottom: spacing(2),
    fontWeight: '600',
  },
  sectionText: {
    marginBottom: spacing(2),
    lineHeight: 22,
  },
  sectionTextLast: {
    lineHeight: 22,
  },

  // Ad Types Carousel Styles (Mobile)
  carouselContainer: {
    padding: spacing(3),
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  carouselContent: {
    paddingHorizontal: spacing(1),
  },
  carouselSeparator: {
    width: spacing(2),
  },

  // Ad Types Grid Styles (Web)
  gridContainer: {
    padding: spacing(3),
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  gridContent: {
    gap: spacing(2),
  },
  gridRow: {
    justifyContent: 'space-between',
    gap: spacing(2),
  },
  gridItem: {
    flex: 1,
    maxWidth: '48%',
  },

  // Ad Card Styles
  adCard: {
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    borderRadius: 8,
    shadowColor: palette.mortar.tokenColorBlack,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
    minWidth: 280,
  },
  adCardImage: {
    width: '100%',
    height: 160,
  },
  adCardContent: {
    padding: spacing(2),
  },
  adCardTitle: {
    marginBottom: spacing(1),
    fontWeight: '600',
  },
  adCardDescription: {
    fontSize: 14,
    lineHeight: 20,
    color: palette.mortar.tokenColorDarkGrey,
  },
}));

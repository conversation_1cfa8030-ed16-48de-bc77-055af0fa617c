import {
  tokenDefault100,
  tokenNeutral0,
} from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV3';
import { Button, Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  isTruthy,
  spacing as mortarSpacing,
} from '@cat-home-experts/react-native-utilities';
import {
  ParamListBase,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import React, { ReactElement, useCallback, useEffect } from 'react';
import { Image, Pressable, ScrollView, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import welcomeBanner from 'src/assets/images/myTeamInvites/welcomeBanner.jpg';
import { config } from 'src/config';
import {
  COOKIE_POLICY_URL,
  MY_TEAM_INVITE,
  PRIVACY_NOTICE_URL,
  TERMS_OF_USE_URL,
  WELCOME_SCREEN,
} from 'src/constants';
import { EVENT_TYPE } from 'src/constants.events';
import { useIsAtMostSmallScreenWidth } from 'src/hooks/useMediaQuery';
import { ScreenBreakpoints } from 'src/hooks/useMediaQuery/ScreenBreakpoints';
import { AllScreensParamList } from 'src/navigation/routes';
import { logEvent } from 'src/services/analytics';
import { openURL } from 'src/utilities/linking';
import { FeatureItems } from './FeatureItems';
import { MY_TEAM_INVITE_COPY } from './constants';

export function MyTeamInvite(): ReactElement {
  const route =
    useRoute<RouteProp<AllScreensParamList, typeof MY_TEAM_INVITE>>();
  const { inviteId } = route.params ?? {};

  const isMobileScreen = useIsAtMostSmallScreenWidth();
  const navigation = useNavigation<NativeStackNavigationProp<ParamListBase>>();

  const handleCreateAccountPress = useCallback(() => {
    if (inviteId) {
      logEvent(EVENT_TYPE.SUBBIE_INVITE_CREATE_ACCOUNT_CLICKED, { inviteId });
      openURL(`${config.subcontractorOnboardingUrl}/${inviteId}`, {
        newTab: false,
      });
    }
  }, [inviteId]);

  const handleLoginPress = useCallback(() => {
    if (inviteId) {
      logEvent(EVENT_TYPE.SUBBIE_INVITE_LOGIN_CLICKED, { inviteId });
      const redirectPath = `my-team?inviteId=${inviteId}`;
      navigation.navigate(WELCOME_SCREEN, {
        redirect: encodeURIComponent(redirectPath),
      });
    }
  }, [navigation, inviteId]);

  const handleTermsOfUsePress = useCallback(() => {
    logEvent(EVENT_TYPE.INVITE_TERMS_OF_USE_CLICKED);
    openURL(TERMS_OF_USE_URL);
  }, []);

  const handlePrivacyPolicyPress = useCallback(() => {
    logEvent(EVENT_TYPE.INVITE_PRIVACY_POLICY_CLICKED);
    openURL(PRIVACY_NOTICE_URL);
  }, []);

  const handleCookiePolicyPress = useCallback(() => {
    logEvent(EVENT_TYPE.INVITE_COOKIE_POLICY_CLICKED);
    openURL(COOKIE_POLICY_URL);
  }, []);

  useEffect(() => {
    if (!isTruthy(inviteId)) {
      navigation.navigate(WELCOME_SCREEN, {
        redirect: 'my-team',
      });
    }
  }, [inviteId, navigation]);

  useEffect(() => {
    if (!inviteId) {
      return;
    }

    logEvent(EVENT_TYPE.INVITE_PAGE_SHOW, { inviteId });
  }, [inviteId]);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[
          styles.scrollViewContentContainer,
          !isMobileScreen && styles.scrollContentContainerLarge,
        ]}
      >
        <View style={styles.header}>
          <View style={styles.bannerContainer}>
            <Image
              source={welcomeBanner}
              style={styles.bannerImage}
              resizeMode="cover"
            />
          </View>
          <View style={styles.welcomePill}>
            <Typography useVariant="bodySemiBold" style={styles.welcomeText}>
              {MY_TEAM_INVITE_COPY.WELCOME}
            </Typography>
          </View>

          <Typography
            useVariant={
              isMobileScreen ? 'headingMDSemiBold' : 'headingLGSemiBold'
            }
            style={[
              styles.title,
              {
                marginTop: isMobileScreen
                  ? mortarSpacing(1.5)
                  : mortarSpacing(3),
                marginBottom: isMobileScreen
                  ? mortarSpacing(0.5)
                  : mortarSpacing(2),
              },
            ]}
          >
            {MY_TEAM_INVITE_COPY.TITLE}
          </Typography>
          <Typography
            useVariant={isMobileScreen ? 'bodySMRegular' : 'subHeadingRegular'}
            style={styles.subtitle}
          >
            {MY_TEAM_INVITE_COPY.SUBTITLE}
          </Typography>
        </View>
        <View
          style={{
            backgroundColor: isMobileScreen ? tokenDefault100 : tokenNeutral0,
          }}
        >
          <FeatureItems />
          <Button
            label={MY_TEAM_INVITE_COPY.BUTTON}
            onPress={handleCreateAccountPress}
            block
            style={styles.button}
          />
          <View style={styles.loginContainer}>
            <Typography useVariant="bodyRegular">
              {MY_TEAM_INVITE_COPY.ALREADY_HAVE_ACCOUNT}
            </Typography>
            <Pressable onPress={handleLoginPress} hitSlop={mortarSpacing(2)}>
              <Typography useVariant="bodySemiBold" style={styles.linkColor}>
                {MY_TEAM_INVITE_COPY.LOG_IN}
              </Typography>
            </Pressable>
            <View
              style={[
                styles.disclaimer,
                {
                  marginTop: isMobileScreen
                    ? mortarSpacing(2)
                    : mortarSpacing(3),
                },
              ]}
            >
              <Typography useVariant="labelRegular">
                {MY_TEAM_INVITE_COPY.RULES_SECTION[0]}
                <Pressable
                  onPress={handleTermsOfUsePress}
                  hitSlop={mortarSpacing(2)}
                >
                  <Typography
                    useVariant="labelRegular"
                    style={styles.linkColor}
                    onPress={handleTermsOfUsePress}
                  >
                    {MY_TEAM_INVITE_COPY.RULES_SECTION[1]}
                  </Typography>
                </Pressable>
                {MY_TEAM_INVITE_COPY.RULES_SECTION[2]}
                <Pressable
                  onPress={handlePrivacyPolicyPress}
                  hitSlop={mortarSpacing(2)}
                >
                  <Typography
                    useVariant="labelRegular"
                    style={styles.linkColor}
                    onPress={handlePrivacyPolicyPress}
                  >
                    {MY_TEAM_INVITE_COPY.RULES_SECTION[3]}
                  </Typography>
                </Pressable>
                {MY_TEAM_INVITE_COPY.RULES_SECTION[4]}
                <Pressable
                  onPress={handleCookiePolicyPress}
                  hitSlop={mortarSpacing(2)}
                >
                  <Typography
                    useVariant="labelRegular"
                    style={styles.linkColor}
                    onPress={handleCookiePolicyPress}
                  >
                    {MY_TEAM_INVITE_COPY.RULES_SECTION[5]}
                  </Typography>
                </Pressable>
                {MY_TEAM_INVITE_COPY.RULES_SECTION[6]}
              </Typography>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = createMortarStyles(({ palette, spacing }) => ({
  container: {
    flex: 1,
    marginTop: spacing(0.25),
  },
  scrollView: {
    backgroundColor: palette.mortar.tokenColorLightBlue,
  },
  scrollViewContentContainer: {
    backgroundColor: palette.mortarV3.tokenNeutral0,
    paddingTop: spacing(3),
  },
  scrollContentContainerLarge: {
    width: '100%',
    maxWidth: ScreenBreakpoints.Medium,
    alignSelf: 'center',
  },
  bannerContainer: {
    width: '100%',
    aspectRatio: 20 / 8,
    borderRadius: spacing(1),
    overflow: 'hidden',
  },
  bannerImage: {
    width: '100%',
    height: '100%',
    alignSelf: 'flex-start',
  },
  header: {
    paddingHorizontal: spacing(3),
  },
  welcomePill: {
    backgroundColor: palette.mortarV3.tokenSuccess500,
    paddingHorizontal: spacing(1),
    marginTop: spacing(3),
    alignSelf: 'flex-start',
    borderRadius: spacing(0.5),
  },
  welcomeText: {
    color: palette.mortarV3.tokenNeutral0,
  },
  title: {
    color: palette.mortarV3.tokenNeutral900,
  },
  subtitle: {
    paddingBottom: spacing(3),
    color: palette.mortarV3.tokenNeutral900,
  },
  linkColor: {
    color: palette.mortarV3.tokenDefault500,
  },
  button: {
    marginVertical: spacing(2.5),
    marginHorizontal: spacing(3),
  },
  loginContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: spacing(2),
  },
  disclaimer: {
    paddingHorizontal: spacing(3),
  },
}));

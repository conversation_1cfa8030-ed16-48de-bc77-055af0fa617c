import React, {
  ReactElement,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { View } from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import {
  Typography,
  LoadingGuard,
  BulletList,
  StatusPillV2,
  StatusPillProps,
} from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  formatCurrency,
  isTruthy,
} from '@cat-home-experts/react-native-utilities';
import {
  BOOKABLE_SERVICE_UNIT_OPTIONS,
  BOOKABLE_SERVICES_DETAIL_STRINGS,
} from 'src/screens/BookableServices/constants';
import { createTestIds } from 'src/utilities/testIds';
import {
  ChevronDownSmall,
  InformationCircle,
  MinusCircle,
  PlusCircle,
  PoundSterling,
} from '@cat-home-experts/mortar-iconography-native';
import { useBookableServiceVersion } from 'src/screens/BookableServices/hooks/useBookableServiceVersion';
import { PageNotFound } from 'src/screens/PageNotFound';
import {
  BOOKABLE_SERVICE_DETAIL_SCREEN,
  BOOKABLE_SERVICES_SCREEN,
  EDIT_BOOKABLE_SERVICE_SCREEN,
  IS_WEB,
} from 'src/constants';
import type { AllScreensParamList } from 'src/navigation/routes';
import { FormGroup, FormGroupScreen } from 'src/components/FormGroup';
import { HtmlRenderer } from 'src/components/HtmlRenderer';
import { showToast } from 'src/components';
import { BookingServiceStatusType } from 'src/data/schemas/api/capi/service-catalog/service';
import { captureException } from 'src/services/datadog';
import { DeleteConfirmationSheetModal } from 'src/components/DeleteConfirmationSheetModal';
import { ActionMenu } from 'src/components/ActionMenu';
import { useFeatureFlag } from 'src/hooks/useFeatureFlag';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { getStatusPillConfiguration } from './utilities/getStatusPillConfiguration';
import { BookableServiceStatusBottomSheet } from './components/BookableServiceStatusBottomSheet';
import { BookableServiceDetailHeaderRight } from './components/BookableServiceDetailHeaderRight';
import { BookableServiceDetailBanner } from './components/BookableServiceDetailBanner';

const TEST_IDS = createTestIds('bookable-services-detail', {
  TITLE: 'title',
  DESCRIPTION_INTRO: 'description-intro',
  PRICING_DETAILS: 'pricing-details',
  INCLUDED_LIST: 'included-list',
  NOT_INCLUDED_LIST: 'not-included-list',
  NOTES: 'notes',
  STATUS_PILL: 'status-pill',
});

export function BookableServicesDetail(): ReactElement {
  const navigation = useNavigation<StackNavigationProp<AllScreensParamList>>();
  const route =
    useRoute<
      RouteProp<AllScreensParamList, typeof BOOKABLE_SERVICE_DETAIL_SCREEN>
    >();
  const id = route.params?.id;
  const versionId = route.params?.versionId;
  const enableBookableServiceActions = useFeatureFlag(
    'enable_bookable_service_actions',
    {
      isFeatureComplete: true,
      enabledInDev: true,
    },
  );

  if (!id || !versionId) {
    throw new InvalidNavParamsError();
  }

  const {
    bookableServiceVersion,
    isLoading,
    publishService,
    hideService,
    archiveService,
    submitServiceForReview,
  } = useBookableServiceVersion(id, versionId);

  const serviceStatus = getStatusPillConfiguration({
    status: bookableServiceVersion?.status,
  });

  const [isVisibilityBottomSheetVisible, setIsVisibilityBottomSheetVisible] =
    useState(false);
  const [isActionMenuVisible, setIsActionMenuVisible] = useState(false);
  const [
    isDeleteConfirmationSheetVisible,
    setIsDeleteConfirmationSheetVisible,
  ] = useState(false);
  const [isButtonLoading, setIsButtonLoading] = useState(false);

  const formattedPrice = useMemo(() => {
    if (!bookableServiceVersion?.lineItems?.length) {
      return 'Price not available';
    }

    const priceItem = bookableServiceVersion.lineItems[0];
    const quantifier = BOOKABLE_SERVICE_UNIT_OPTIONS.find(
      (option) => option.value === priceItem.quantifier,
    );

    return `from ${formatCurrency(priceItem.priceInPence)}/${quantifier?.label?.toLocaleLowerCase()}`;
  }, [bookableServiceVersion]);

  const filteredIncludedItems = useMemo(() => {
    return (
      bookableServiceVersion?.whatIsIncluded?.filter(
        (item) => item && item.trim() !== '',
      ) ?? []
    );
  }, [bookableServiceVersion]);

  const filteredNotIncludedItems = useMemo(() => {
    return (
      bookableServiceVersion?.whatIsNotIncluded?.filter(
        (item) => item && item.trim() !== '',
      ) ?? []
    );
  }, [bookableServiceVersion]);

  const handleSubmitForReview = useCallback(async () => {
    setIsButtonLoading(true);
    try {
      await submitServiceForReview();
      logEvent(EVENT_TYPE.BOOKABLE_SERVICES_SUBMIT_FOR_REVIEW_SUCCESS, {
        serviceId: id,
        versionId: versionId,
        categoryId: bookableServiceVersion?.categoryId,
      });
      showToast({
        text1: 'Bookable service submitted for review',
        type: 'success',
      });
      navigation.navigate(BOOKABLE_SERVICES_SCREEN);
    } catch (error) {
      captureException(error, {
        source: 'BookableServicesDetail',
        method: 'handleSubmitForReview',
        bookableServiceId: id,
        bookableServiceVersionId: versionId,
      });

      showToast({
        text1: 'Error submitting service for review',
        type: 'error',
      });
    } finally {
      setIsButtonLoading(false);
    }
  }, [
    id,
    versionId,
    navigation,
    submitServiceForReview,
    bookableServiceVersion?.categoryId,
  ]);

  const handleEditPress = useCallback(() => {
    navigation.navigate(EDIT_BOOKABLE_SERVICE_SCREEN, {
      id,
      versionId,
    });
  }, [id, versionId, navigation]);

  const showVisibilityBottomSheet = () => {
    setIsVisibilityBottomSheetVisible(true);
  };

  const hideVisibilityBottomSheet = () => {
    setIsVisibilityBottomSheetVisible(false);
  };

  const showActionMenu = () => {
    setIsActionMenuVisible(true);
  };

  const hideActionMenu = () => {
    setIsActionMenuVisible(false);
  };

  const showDeleteConfirmationSheet = () => {
    setIsDeleteConfirmationSheetVisible(true);
  };

  const hideDeleteConfirmationSheet = () => {
    setIsDeleteConfirmationSheetVisible(false);
  };

  const handleDeleteService = async () => {
    try {
      await archiveService();
      logEvent(EVENT_TYPE.BOOKABLE_SERVICES_DELETE_SUCCESS, {
        serviceId: id,
        versionId: versionId,
        categoryId: bookableServiceVersion?.categoryId,
      });
      showToast({
        text1: 'Bookable service deleted',
        type: 'success',
      });

      if (IS_WEB) {
        navigation.navigate(BOOKABLE_SERVICES_SCREEN);
      } else {
        navigation.replace(BOOKABLE_SERVICES_SCREEN);
      }
    } catch (error) {
      captureException(error, {
        source: 'BookableServicesDetail',
        method: 'handleDeleteService',
        bookableServiceId: id,
        bookableServiceVersionId: versionId,
      });

      showToast({
        text1: 'Error deleting bookable service',
        type: 'error',
      });
    } finally {
      setIsDeleteConfirmationSheetVisible(false);
    }
  };

  const handleBookableServiceStatusChange = async (
    newStatus: BookingServiceStatusType,
  ) => {
    try {
      if (newStatus === BookingServiceStatusType.PublishedActive) {
        await publishService();
        logEvent(EVENT_TYPE.BOOKABLE_SERVICES_PUBLISH_SUCCESS, {
          serviceId: id,
          versionId: versionId,
          categoryId: bookableServiceVersion?.categoryId,
        });
        showToast({
          text1: 'Bookable service published',
          type: 'success',
        });
      } else if (newStatus === BookingServiceStatusType.PublishedInactive) {
        await hideService();
        logEvent(EVENT_TYPE.BOOKABLE_SERVICES_HIDE_SUCCESS, {
          serviceId: id,
          versionId: versionId,
          categoryId: bookableServiceVersion?.categoryId,
        });
        showToast({
          text1: 'Bookable service hidden',
          type: 'success',
        });
      }
    } catch (error) {
      captureException(error, {
        source: 'BookableServicesDetail',
        method: 'handleBookableServiceStatusChange',
        bookableServiceId: id,
        bookableServiceVersionId: versionId,
        newStatus,
      });

      showToast({
        text1: 'Error changing bookable service status',
        type: 'error',
      });
    } finally {
      hideVisibilityBottomSheet();
    }
  };

  const screenButtonProps = useMemo(() => {
    if (!enableBookableServiceActions) {
      return undefined;
    }

    if (bookableServiceVersion?.status === BookingServiceStatusType.Draft) {
      return {
        label: BOOKABLE_SERVICES_DETAIL_STRINGS.SUBMIT_FOR_REVIEW_BUTTON,
        onPress: handleSubmitForReview,
        isLoading: isButtonLoading,
      };
    }

    if (bookableServiceVersion?.status === BookingServiceStatusType.Rejected) {
      return {
        label: BOOKABLE_SERVICES_DETAIL_STRINGS.EDIT_AND_RESUBMIT_BUTTON,
        onPress: handleEditPress,
        isLoading: isButtonLoading,
      };
    }

    return undefined;
  }, [
    bookableServiceVersion,
    handleEditPress,
    handleSubmitForReview,
    isButtonLoading,
    enableBookableServiceActions,
  ]);

  const renderStatusPill = useCallback(() => {
    const pillStatuses = [
      BookingServiceStatusType.Draft,
      BookingServiceStatusType.PublishedActive,
      BookingServiceStatusType.PublishedInactive,
    ];

    const visibilityPillStatuses = [
      BookingServiceStatusType.PublishedActive,
      BookingServiceStatusType.PublishedInactive,
    ];

    if (
      bookableServiceVersion?.status &&
      pillStatuses.includes(bookableServiceVersion.status) &&
      isTruthy(serviceStatus)
    ) {
      const isVisibilityStatus = visibilityPillStatuses.includes(
        bookableServiceVersion.status,
      );

      const visibilityProps: Partial<StatusPillProps> = {
        rightIcon: ChevronDownSmall,
        onPress: showVisibilityBottomSheet,
      };

      return (
        <StatusPillV2
          size="large"
          label={serviceStatus.label}
          variant={serviceStatus.variant}
          labelTypographyVariant="labelSemiBold"
          {...(isVisibilityStatus && enableBookableServiceActions
            ? visibilityProps
            : {})}
          style={styles.statusPill}
          testId={TEST_IDS.STATUS_PILL}
        />
      );
    }

    return null;
  }, [bookableServiceVersion, serviceStatus, enableBookableServiceActions]);

  useEffect(() => {
    if (!enableBookableServiceActions) {
      return;
    }

    navigation.setOptions({
      headerRight: () => (
        <BookableServiceDetailHeaderRight
          onEditPress={handleEditPress}
          onActionMenuEllipsisPress={showActionMenu}
          onDeletePress={showDeleteConfirmationSheet}
        />
      ),
    });
  }, [handleEditPress, navigation, enableBookableServiceActions]);

  if (!bookableServiceVersion && !isLoading) {
    return <PageNotFound fallbackScreenToNavigate={BOOKABLE_SERVICES_SCREEN} />;
  }

  return (
    <View style={styles.flex} testID={TEST_IDS.ROOT}>
      <LoadingGuard style={styles.flex} spinnerSize={40}>
        {!isLoading && bookableServiceVersion && (
          <FormGroupScreen buttonProps={screenButtonProps}>
            <BookableServiceDetailBanner
              serviceVersion={bookableServiceVersion}
            />
            <FormGroup>
              {renderStatusPill()}
              <View>
                <Typography useVariant="bodySMSemiBold" isMuted>
                  {bookableServiceVersion.categoryName}
                </Typography>
                <Typography
                  useVariant="headingSMSemiBold"
                  testID={TEST_IDS.TITLE}
                >
                  {bookableServiceVersion.name}
                </Typography>
              </View>
              <HtmlRenderer
                html={bookableServiceVersion.description}
                dom={{ matchContents: true }}
              />
            </FormGroup>

            <FormGroup
              header={{
                icon: PoundSterling,
                title: BOOKABLE_SERVICES_DETAIL_STRINGS.PRICING_DETAILS_TITLE,
              }}
            >
              <Typography useVariant="bodySemiBold">
                {formattedPrice}
              </Typography>
              {isTruthy(bookableServiceVersion?.lineItems?.[0]?.notes) && (
                <Typography useVariant="bodyRegular">
                  {bookableServiceVersion?.lineItems?.[0]?.notes}
                </Typography>
              )}
            </FormGroup>

            {/*Included list section*/}
            {filteredIncludedItems.length > 0 && (
              <FormGroup
                header={{
                  icon: PlusCircle,
                  title: BOOKABLE_SERVICES_DETAIL_STRINGS.INCLUDED_TITLE,
                }}
              >
                <BulletList
                  list={filteredIncludedItems.map((item) => [
                    {
                      value: item,
                    },
                  ])}
                />
              </FormGroup>
            )}

            {/*Not included list section*/}
            {filteredNotIncludedItems.length > 0 && (
              <FormGroup
                header={{
                  icon: MinusCircle,
                  title: BOOKABLE_SERVICES_DETAIL_STRINGS.NOT_INCLUDED_TITLE,
                }}
              >
                <BulletList
                  list={filteredNotIncludedItems.map((item) => [
                    {
                      value: item,
                    },
                  ])}
                />
              </FormGroup>
            )}

            {/*Important notes section*/}
            {bookableServiceVersion.homeownerNotes &&
              bookableServiceVersion.homeownerNotes.trim() !== '' && (
                <FormGroup
                  header={{
                    icon: InformationCircle,
                    title: BOOKABLE_SERVICES_DETAIL_STRINGS.NOTES_TITLE,
                  }}
                >
                  <Typography useVariant="bodyRegular">
                    {bookableServiceVersion?.homeownerNotes}
                  </Typography>
                </FormGroup>
              )}
          </FormGroupScreen>
        )}
      </LoadingGuard>
      <BookableServiceStatusBottomSheet
        visible={isVisibilityBottomSheetVisible}
        onDismiss={hideVisibilityBottomSheet}
        bookableServiceStatus={bookableServiceVersion?.status}
        onBookableServiceStatusChange={handleBookableServiceStatusChange}
      />
      <ActionMenu
        visible={isActionMenuVisible}
        onDismiss={hideActionMenu}
        actions={[
          {
            label: BOOKABLE_SERVICES_DETAIL_STRINGS.DELETE_BUTTON,
            action: () => {
              showDeleteConfirmationSheet();
              hideActionMenu();
            },
            style: styles.deleteAction,
          },
        ]}
      />
      <DeleteConfirmationSheetModal
        presented={isDeleteConfirmationSheetVisible}
        onDismiss={hideDeleteConfirmationSheet}
        onDelete={handleDeleteService}
        title={BOOKABLE_SERVICES_DETAIL_STRINGS.DELETE_SERVICE_TITLE}
        subtitle={BOOKABLE_SERVICES_DETAIL_STRINGS.DELETE_SERVICE_SUBTITLE(
          bookableServiceVersion?.name,
        )}
        yesButtonText={BOOKABLE_SERVICES_DETAIL_STRINGS.DELETE_BUTTON}
        noButtonText={BOOKABLE_SERVICES_DETAIL_STRINGS.CANCEL_BUTTON}
      />
    </View>
  );
}

const styles = createMortarStyles(({ palette }) => ({
  flex: {
    flex: 1,
    backgroundColor: palette.mortarV3.tokenDefault100,
  },
  statusPill: {
    alignSelf: 'flex-start',
  },
  deleteAction: {
    color: palette.mortarV3.tokenPrimary700,
  },
}));

BookableServicesDetail.testIds = TEST_IDS;

import React, { ReactElement } from 'react';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { LoadingGuard } from '@cat-home-experts/react-native-components';
import {
  CreateBookableServiceSchema,
  Quantifier,
} from 'src/data/schemas/api/capi/service-catalog/service';
import { AllScreensParamList } from 'src/navigation/routes';
import {
  BOOKABLE_SERVICE_DETAIL_SCREEN,
  EDIT_BOOKABLE_SERVICE_SCREEN,
} from 'src/constants';
import { captureException } from 'src/services/datadog';
import { showToast } from 'src/components';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { useBookableServiceForm } from './hooks/useBookableServiceForm';
import { BookableServiceForm } from './components/BookableServiceForm';
import { useBookableServiceVersion } from './hooks/useBookableServiceVersion';

const TEST_IDS = createTestIds('edit-bookable-service', {});

export const EditBookableService = (): ReactElement => {
  const navigation = useNavigation<StackNavigationProp<AllScreensParamList>>();
  const { params } =
    useRoute<
      RouteProp<AllScreensParamList, typeof EDIT_BOOKABLE_SERVICE_SCREEN>
    >();

  const { id, versionId } = params ?? {};

  if (!id || !versionId) {
    throw new InvalidNavParamsError();
  }

  const { bookableServiceVersion, editService, isLoading } =
    useBookableServiceVersion(id, versionId);

  const form = useBookableServiceForm({
    initialValues: {
      name: bookableServiceVersion?.name ?? '',
      description: bookableServiceVersion?.description ?? '',
      categoryId: bookableServiceVersion?.categoryId ?? 0,
      priceInPence: bookableServiceVersion?.lineItems?.[0].priceInPence ?? 0,
      quantifier:
        bookableServiceVersion?.lineItems?.[0].quantifier ?? Quantifier.PerJob,
      homeownerPriceRelatedNotes:
        bookableServiceVersion?.lineItems?.[0]?.notes ?? '',
      whatIsIncluded: bookableServiceVersion?.whatIsIncluded ?? [],
      whatIsNotIncluded: bookableServiceVersion?.whatIsNotIncluded ?? [],
      homeownerNotes: bookableServiceVersion?.homeownerNotes ?? '',
    },
  });

  const onSubmit = async (service: CreateBookableServiceSchema) => {
    try {
      const response = await editService(service);
      logEvent(EVENT_TYPE.BOOKABLE_SERVICES_EDIT_SUCCESS, {
        serviceId: response.serviceId,
        versionId: response.id,
        categoryId: service.categoryId,
      });
      try {
        navigation.replace(BOOKABLE_SERVICE_DETAIL_SCREEN, {
          id: response.serviceId,
          versionId: response.id,
        });
      } catch (e) {
        navigation.navigate(BOOKABLE_SERVICE_DETAIL_SCREEN, {
          id: response.serviceId,
          versionId: response.id,
        });
      }
    } catch (error) {
      captureException(error, {
        source: 'EditBookableService',
        method: 'onSubmit',
      });
      showToast({
        text1: 'Error editing service',
        type: 'error',
      });
    }
  };

  return (
    <LoadingGuard style={styles.container}>
      {!isLoading && (
        <BookableServiceForm form={form} isEditMode onSubmit={onSubmit} />
      )}
    </LoadingGuard>
  );
};

EditBookableService.testIds = TEST_IDS;

const styles = createMortarStyles(() => ({
  container: {
    flex: 1,
  },
}));

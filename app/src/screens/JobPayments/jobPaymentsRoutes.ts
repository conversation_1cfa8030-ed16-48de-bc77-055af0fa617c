import {
  JO<PERSON>_PAYMENTS_CANCEL_REQUEST_SCREEN,
  JOB_PAYMENTS_CHOOSE_JOB_REQUEST_SCREEN,
  JOB_PAYMENTS_ENABLE_TAP_EDUCATION_VIDEO_SCREEN,
  JO<PERSON>_PAYMENTS_ENABLE_TAP_SCREEN,
  JOB_PAYMENTS_EXPORT_STATEMENT_SCREEN,
  JOB_PAYMENTS_NEW_REQUEST_SCREEN,
  JOB_PAYMENTS_OFF_PLATFORM_NEW_REQUEST_SCREEN,
  JOB_PAYMENTS_ONBOARDING_PRIVACY_SCREEN,
  JOB_PAYMENTS_PAY_BY_PHONE_SCREEN,
  JOB_PAYMENTS_PAYMENT_DETAILS_SCREEN,
  JOB_PAYMENTS_PAYMENT_REQUEST_EDUCATION,
  JOB_PAYMENTS_QUOTE_NEW_PAYMENT_SCREEN,
  JO<PERSON>_PAYMENTS_SCREEN,
  JOB_PAYMENTS_SETUP_HELP_SCREEN,
  JO<PERSON>_PAYMENTS_TAP_TO_PAY_ENABLED,
  JO<PERSON>_PAYMENTS_TAX_INFORMATION_SCREEN,
} from 'src/constants';
import type { RouteItem } from 'src/navigation/types/routeTypes';

import { JobPaymentsPayByPhone } from 'src/screens/JobPayments/screens/JobPaymentsPayByPhone';
import { JobPaymentsPaymentRequestEducation } from 'src/screens/JobPayments/screens/JobPaymentsPaymentRequestEducation';
import { JobPaymentsTapToPayEnabled } from 'src/screens/JobPayments/screens/JobPaymentsTapToPayEnabled';
import { JobPayments } from './screens/JobPayments';
import { JobPaymentsCancelRequest } from './screens/JobPaymentsCancelRequest';
import { JobPaymentsChooseJobRequest } from './screens/JobPaymentsChooseJobRequest';
import { JobPaymentsEnableTap } from './screens/JobPaymentsEnableTap';
import { JobPaymentsEnableTapEducationVideo } from './screens/JobPaymentsEnableTapEducationVideo';
import { JobPaymentsExportStatementRequest } from './screens/JobPaymentsExportStatementRequest';
import { JobPaymentsNewRequest } from './screens/JobPaymentsNewRequest';
import { JobPaymentsOffPlatformNewRequest } from './screens/JobPaymentsOffPlatformNewRequest';
import { JobPaymentsPayByPhoneScreenParams } from './screens/JobPaymentsPayByPhone.web';
import { JobPaymentsPaymentDetail } from './screens/JobPaymentsPaymentDetails';
import { JobPaymentsPrivacy } from './screens/JobPaymentsPrivacy';
import { JobPaymentsQuoteNewPayment } from './screens/JobPaymentsQuoteNewPayment';
import { JobPaymentsSetupHelp } from './screens/JobPaymentsSetupHelp';
import { JobPaymentsTaxInformation } from './screens/JobPaymentsTaxInformation';

export type JobPaymentsParamList = {
  [JOB_PAYMENTS_SCREEN]: undefined;
  [JOB_PAYMENTS_ONBOARDING_PRIVACY_SCREEN]: undefined;
  [JOB_PAYMENTS_CANCEL_REQUEST_SCREEN]: {
    paymentLinkId: string;
    jobId?: string;
    channelId?: string;
  };
  [JOB_PAYMENTS_SETUP_HELP_SCREEN]: undefined;
  [JOB_PAYMENTS_TAX_INFORMATION_SCREEN]: undefined;
  [JOB_PAYMENTS_NEW_REQUEST_SCREEN]: {
    jobId?: string;
  };
  [JOB_PAYMENTS_PAYMENT_DETAILS_SCREEN]: {
    paymentLinkId: string;
    channelId?: string;
  };
  [JOB_PAYMENTS_OFF_PLATFORM_NEW_REQUEST_SCREEN]: undefined;
  [JOB_PAYMENTS_CHOOSE_JOB_REQUEST_SCREEN]: {
    jobId?: string;
  };
  [JOB_PAYMENTS_ENABLE_TAP_SCREEN]: undefined;
  [JOB_PAYMENTS_QUOTE_NEW_PAYMENT_SCREEN]: {
    quoteId: string;
    quotePenceTotal?: string;
    jobId?: string | null;
  };
  [JOB_PAYMENTS_ENABLE_TAP_EDUCATION_VIDEO_SCREEN]: { buttonsShown?: boolean };
  [JOB_PAYMENTS_PAYMENT_REQUEST_EDUCATION]: undefined;
  [JOB_PAYMENTS_TAP_TO_PAY_ENABLED]: undefined;
  [JOB_PAYMENTS_PAY_BY_PHONE_SCREEN]: JobPaymentsPayByPhoneScreenParams;
  [JOB_PAYMENTS_EXPORT_STATEMENT_SCREEN]: undefined;
};

export const jobPaymentsRoutes = ({
  enableTapToPay,
  enablePayByPhone,
  enablePaymentRequests,
  isImpersonation,
}: {
  enableTapToPay: boolean;
  enablePayByPhone: boolean;
  enablePaymentRequests: boolean;
  isImpersonation: boolean;
}): RouteItem[] => {
  if (isImpersonation) {
    return [];
  }

  return [
    {
      key: 'job-payments',
      name: JOB_PAYMENTS_SCREEN,
      component: JobPayments,
      path: 'job-payments',
      iconName: 'credit-card',
      navGroup: 'job-payments',
      options: {
        headerShown: false,
        title: 'Payments',
      },
    },
    {
      key: 'job-payments-onboarding-privacy',
      name: JOB_PAYMENTS_ONBOARDING_PRIVACY_SCREEN,
      component: JobPaymentsPrivacy,
      path: 'job-payments/privacy-policy',
      options: {
        title: 'Checkatrade Privacy Notice',
        headerShown: false,
      },
    },
    {
      key: 'setup-job-payments-help',
      name: JOB_PAYMENTS_SETUP_HELP_SCREEN,
      component: JobPaymentsSetupHelp,
      path: 'job-payments/learn-more',
      navGroup: 'job-payments',
      options: {
        title: 'Learn more',
      },
    },
    {
      key: 'job-payments-tax-information',
      name: JOB_PAYMENTS_TAX_INFORMATION_SCREEN,
      component: JobPaymentsTaxInformation,
      path: 'job-payments/tax-information',
      navGroup: 'job-payments',
      options: {
        headerShown: false,
        title: 'Learn more',
      },
    },
    ...(enablePaymentRequests
      ? [
          {
            key: 'job-payments-new-quote-payment-request',
            name: JOB_PAYMENTS_QUOTE_NEW_PAYMENT_SCREEN,
            component: JobPaymentsQuoteNewPayment,
            path: 'job-payments/new-quote-payment-request',
            navGroup: 'job-payments',
            options: {
              headerShown: true,
              title: 'New payment request',
            },
          } satisfies RouteItem,
          {
            key: 'job-payments-new-payment-request',
            name: JOB_PAYMENTS_NEW_REQUEST_SCREEN,
            component: JobPaymentsNewRequest,
            path: 'job-payments/new-payment-request',
            navGroup: 'job-payments',
            options: { headerShown: true },
          } satisfies RouteItem,
          {
            key: 'job-payments-cancel-request',
            name: JOB_PAYMENTS_CANCEL_REQUEST_SCREEN,
            component: JobPaymentsCancelRequest,
            path: 'job-payments/:paymentLinkId/cancel-request',
            navGroup: 'job-payments',
            options: {
              headerShown: true,
              title: 'Cancel payment request',
            },
          } satisfies RouteItem,
          {
            key: 'job-payments-payment-details',
            name: JOB_PAYMENTS_PAYMENT_DETAILS_SCREEN,
            component: JobPaymentsPaymentDetail,
            path: 'job-payments/payment-details/:paymentLinkId',
            navGroup: 'job-payments',
            options: {
              headerShown: false,
            },
          } satisfies RouteItem,
          {
            key: 'job-payments-off-platform-new-payment-request',
            name: JOB_PAYMENTS_OFF_PLATFORM_NEW_REQUEST_SCREEN,
            component: JobPaymentsOffPlatformNewRequest,
            path: 'job-payments/new-customer-payment-request',
            navGroup: 'job-payments',
            options: {
              headerShown: true,
              title: 'New payment request',
            },
          } satisfies RouteItem,
        ]
      : []),
    ...(enablePayByPhone && enablePaymentRequests
      ? [
          {
            key: 'job-payments-pay-by-phone',
            name: JOB_PAYMENTS_PAY_BY_PHONE_SCREEN,
            component: JobPaymentsPayByPhone,
            path: 'job-payments/pay-by-phone',
            navGroup: 'job-payments',
            options: {
              headerShown: true,
              title: 'Take payment by phone',
            },
          } satisfies RouteItem,
        ]
      : []),
    {
      key: 'job-payments-payment-request-education',
      name: JOB_PAYMENTS_PAYMENT_REQUEST_EDUCATION,
      component: JobPaymentsPaymentRequestEducation,
      path: 'job-payments/payment-request-education',
      options: {
        presentation: 'modal',
        headerShown: false,
      },
    } satisfies RouteItem,
    ...(enableTapToPay
      ? [
          {
            key: 'job-payments-enable-tap',
            name: JOB_PAYMENTS_ENABLE_TAP_SCREEN,
            component: JobPaymentsEnableTap,
            path: 'job-payments/enable-tap',
            options: { headerShown: false },
          } satisfies RouteItem,
          {
            key: 'job-payments-enable-tap-education-video',
            name: JOB_PAYMENTS_ENABLE_TAP_EDUCATION_VIDEO_SCREEN,
            component: JobPaymentsEnableTapEducationVideo,
            path: 'job-payments/enable-tap-video',
            options: {
              headerShown: false,
            },
          } satisfies RouteItem,
          {
            key: 'job-payments-tap-to-pay-enabled',
            name: JOB_PAYMENTS_TAP_TO_PAY_ENABLED,
            component: JobPaymentsTapToPayEnabled,
            path: 'job-payments/tap-to-pay-enabled',
            options: {
              headerShown: false,
              gestureEnabled: false,
            },
          } satisfies RouteItem,
        ]
      : ([] as const)),
    {
      key: 'job-payments-choose-job-request',
      name: JOB_PAYMENTS_CHOOSE_JOB_REQUEST_SCREEN,
      component: JobPaymentsChooseJobRequest,
      path: 'job-payments/choose-job-request',
      navGroup: 'job-payments',
      options: {
        headerShown: true,
      },
    },
    {
      key: 'job-payments-export-statement-request',
      name: JOB_PAYMENTS_EXPORT_STATEMENT_SCREEN,
      component: JobPaymentsExportStatementRequest,
      path: 'job-payments/export-statement-request',
      navGroup: 'job-payments',
      options: {
        headerShown: true,
        title: 'Export statement',
      },
    } satisfies RouteItem,
  ];
};

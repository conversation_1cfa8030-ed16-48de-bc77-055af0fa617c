import React, { useEffect, useMemo, useState } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ScrollView, View } from 'react-native';
import { noop } from 'lodash';
import { startOfToday } from 'date-fns';
import {
  RouteProp,
  StackActions,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { AllScreensParamList } from 'src/navigation/routes';
import {
  createMortarStyles,
  createTestIds,
  isTruthy,
} from '@cat-home-experts/react-native-utilities';
import {
  Button,
  InputField,
  Typography,
} from '@cat-home-experts/react-native-components';

import { DatePicker } from 'src/components/primitives/DatePicker';
import { getEndOfDayOfXDaysFromToday } from 'src/data/schemas/helpers/getEndOfDayOfXDaysFromToday';
import { useDebouncedValue } from 'src/hooks/useDebouncedValue';
import { formatCurrency } from 'src/utilities/money/formatCurrency';
import {
  JOB_PAYMENTS_QUOTE_NEW_PAYMENT_SCREEN,
  QUOTES_SCREEN,
} from 'src/constants';
import {
  CurrencyType,
  PaymentRequestOffPlatformJobRequest,
  SplitType,
} from 'src/data/schemas/api/capi/payments/payment-request';
import { useUserContext } from 'src/hooks/useUser';
import { showToast } from 'src/components';

import { useOffPlatformPaymentRequest } from 'src/screens/JobPayments/hooks/useOffPlatformPaymentRequest';
import { useQuoteDetails } from 'src/screens/QuotesAndInvoices/QuoteDetails/useQuoteDetails';
import { OffPlatformCategoryType } from 'src/screens/JobPayments/types';
import { captureException } from 'src/services/datadog';
import {
  isValidEmail,
  validateConsumerPhoneNumber,
} from 'src/utilities/validation';
import { splitFirstAndLastName } from 'src/utilities/name/formatName';
import { isValidConsumerName } from 'src/utilities/validation/name';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { useGetCostBreakdown } from '../hooks/useGetCostBreakdown';
import { JobPaymentsBreakdownCosts } from '../components/JobPaymentsBreakdownCosts';
import { currencyFilter, formatCurrencyInput } from '../utilities/utilities';
import {
  BREAKDOWN_COST_API_DELAY,
  ERROR_LABELS,
  NEW_PAYMENT_AMOUNT_MAX_ERROR,
  NEW_PAYMENT_AMOUNT_MIN_ERROR,
  NEW_PAYMENT_REQUEST_MAX_DAYS_DUE_DATE,
  QUOTE_PAYMENT_REQUEST_LABELS,
  QUOTE_PAYMENT_REQUEST_WARNINGS,
} from '../constants';
import { useMaxPaymentsValue } from '../hooks/useMaxPaymentsValue';
import { useMinPaymentsValue } from '../hooks/useMinPaymentsValue';
import { useMarketplaceJobDetails } from '../../MarketplaceJobs/hooks';
import { usePaymentRequest } from '../hooks/usePaymentRequest';
import { JobPaymentsOffPlatformCategoryDropdown } from '../components/JobPaymentsOffPlatformCategoryDropdown';

const TEST_IDS = createTestIds('job-payments-quote-new-payment', {
  AMOUNT_TITLE: 'amount-title',
  AMOUNT_VALUE: 'amount-value',
  CURRENCY_SYMBOL_BUTTON: 'currency-symbol-button',
  PERCENTAGE_SYMBOL_BUTTON: 'percentage-symbol-button',
  AMOUNT_INPUT: 'amount-input',
  DESCRIPTION_INPUT: 'description-input',
  PERCENTAGE_INPUT: 'percentage-input',
  DUE_DATE_INPUT: 'date-input',
  CREATE_BUTTON: 'create-button',
  CONSUMER_FIRST_NAME_INPUT: 'consumer-first-name-input',
  CONSUMER_LAST_NAME_INPUT: 'consumer-last-name-input',
  CONSUMER_EMAIL_INPUT: 'consumer-email-input',
  CONSUMER_PHONE_INPUT: 'consumer-phone-input',
});

export function JobPaymentsQuoteNewPayment(): ReturnType<React.FC> {
  const { bottom } = useSafeAreaInsets();
  const { companyId } = useUserContext();
  const navigation = useNavigation();
  const route =
    useRoute<
      RouteProp<
        AllScreensParamList,
        typeof JOB_PAYMENTS_QUOTE_NEW_PAYMENT_SCREEN
      >
    >();
  const { jobId, quoteId, quotePenceTotal } = route.params ?? {};
  if (!quoteId) {
    throw new InvalidNavParamsError();
  }

  const createPaymentRequestMutation = usePaymentRequest();
  const createOffPlatformPaymentRequestMutation =
    useOffPlatformPaymentRequest();
  const { job } = useMarketplaceJobDetails(jobId);
  const { data: quote } = useQuoteDetails(quoteId);
  const minAmountPence = useMinPaymentsValue();
  const maxAmountPence = useMaxPaymentsValue();

  const [percentageTypeSelected, setPercentageTypeSelected] =
    useState<boolean>(false);
  const [amount, setAmount] = useState<string>(quotePenceTotal ?? '0');
  const [percentage, setPercentage] = useState<string>('');
  const [paymentRequestDescription, setPaymentRequestDescription] =
    useState<string>('');
  const [consumerFirstName, setConsumerFirstName] = useState<string>('');
  const [consumerLastName, setConsumerLastName] = useState<string>('');
  const [consumerEmail, setConsumerEmail] = useState<string>('');
  const [consumerPhone, setConsumerPhone] = useState<string>('');
  const [dueDate, setDueDate] = useState<Date>(new Date());
  const [jobCategory, setJobCategory] = useState<OffPlatformCategoryType>();

  const [debouncedAmount] = useDebouncedValue(
    Number(amount),
    BREAKDOWN_COST_API_DELAY,
  );

  const waitingForDebounce = Number(amount) !== debouncedAmount;

  const { data, isLoading: breakdownLoading } =
    useGetCostBreakdown(debouncedAmount);
  const curriedData = useMemo(
    () => ({ ...(data ?? {}), amount: Number(amount) }),

    [amount, data],
  );

  useEffect(() => {
    if (quote?.homeownerDetails.emailAddress) {
      setConsumerEmail(quote?.homeownerDetails.emailAddress);
    }

    if (quote?.homeownerDetails.phoneNumber) {
      setConsumerPhone(quote?.homeownerDetails.phoneNumber);
    }

    if (quote?.homeownerDetails.name) {
      const name = splitFirstAndLastName(quote.homeownerDetails.name);
      if (name.firstName) {
        setConsumerFirstName(name.firstName);
      }

      if (name.lastName) {
        setConsumerLastName(name.lastName);
      }
    }
  }, [quote?.homeownerDetails]);

  const meetsMinimumAmount = useMemo(() => {
    return curriedData.amount >= minAmountPence;
  }, [curriedData.amount, minAmountPence]);

  const amountDoesNotExceedMax = useMemo(() => {
    return curriedData.amount <= maxAmountPence;
  }, [curriedData.amount, maxAmountPence]);

  const amountWarning = useMemo(() => {
    switch (true) {
      case !curriedData.amount:
        return undefined;
      case !meetsMinimumAmount:
        return `${NEW_PAYMENT_AMOUNT_MIN_ERROR} ${formatCurrency(minAmountPence)}`;
      case !amountDoesNotExceedMax:
        return `${NEW_PAYMENT_AMOUNT_MAX_ERROR} ${formatCurrency(maxAmountPence)}`;
      default:
        return undefined;
    }
  }, [
    curriedData.amount,
    amountDoesNotExceedMax,
    maxAmountPence,
    meetsMinimumAmount,
    minAmountPence,
  ]);

  const percentageWarning = useMemo(() => {
    const percentageAsNumber = Number(percentage);

    switch (true) {
      case percentage === '':
        return undefined;
      case isNaN(percentageAsNumber):
      case percentageAsNumber < 1:
      case percentageAsNumber > 100:
      case Number(amount) < minAmountPence:
        return QUOTE_PAYMENT_REQUEST_WARNINGS.PERCENTAGE_INPUT;
      default:
        return undefined;
    }
  }, [percentage, amount, minAmountPence]);

  const firstNameWarning = useMemo(() => {
    if (!isValidConsumerName(consumerFirstName)) {
      return ERROR_LABELS.INVALID_FIRST_NAME;
    }

    return undefined;
  }, [consumerFirstName]);

  const lastNameWarning = useMemo(() => {
    if (!isValidConsumerName(consumerLastName)) {
      return ERROR_LABELS.INVALID_LAST_NAME;
    }

    return undefined;
  }, [consumerLastName]);

  const emailWarning = useMemo(() => {
    if (!isValidEmail(consumerEmail)) {
      return ERROR_LABELS.INVALID_EMAIL;
    }

    return undefined;
  }, [consumerEmail]);

  const phoneWarning = useMemo(() => {
    if (!validateConsumerPhoneNumber(consumerPhone)) {
      return ERROR_LABELS.INVALID_PHONE_NUMBER;
    }

    return undefined;
  }, [consumerPhone]);

  // button enablement
  const hasRequiredDetails = useMemo(() => {
    return (
      amount &&
      dueDate &&
      amountDoesNotExceedMax &&
      meetsMinimumAmount &&
      !percentageWarning &&
      !waitingForDebounce &&
      !breakdownLoading &&
      // either has a job associated or all required details for an off platform payment
      (job ||
        (jobCategory &&
          isValidEmail(consumerEmail) &&
          validateConsumerPhoneNumber(consumerPhone) &&
          quote?.homeownerDetails.postcode &&
          isValidConsumerName(consumerFirstName) &&
          isValidConsumerName(consumerLastName)))
    );
  }, [
    amount,
    dueDate,
    percentageWarning,
    amountDoesNotExceedMax,
    meetsMinimumAmount,
    waitingForDebounce,
    breakdownLoading,
    job,
    jobCategory,
    consumerEmail,
    consumerPhone,
    consumerFirstName,
    consumerLastName,
    quote?.homeownerDetails.postcode,
  ]);

  const updatePercentage = (percentageInput: string) => {
    setPercentage(percentageInput);
    setAmount(`${(Number(percentageInput) / 100) * Number(quotePenceTotal)}`);
  };

  const toggleInputSelection = () => {
    setAmount('');
    setPercentage('');
    setPercentageTypeSelected(!percentageTypeSelected);
  };

  const handleSubmit = async () => {
    if (!quoteId) {
      return;
    }

    const splits: [
      { type: SplitType; amount: { value: number; currency: CurrencyType } },
      { type: SplitType; amount: { value: number; currency: CurrencyType } },
    ] = [
      {
        amount: { value: data!.serviceCharge, currency: CurrencyType.GBP },
        type: SplitType.Checkatrade,
      },
      {
        amount: { value: Number(amount), currency: CurrencyType.GBP },
        type: SplitType.Trader,
      },
    ];

    try {
      const amountObject = {
        value: Number(amount),
        currency: CurrencyType.GBP,
      };

      if (job) {
        const payload = {
          paymentRequest: {
            amount: amountObject,
            description: isTruthy(paymentRequestDescription)
              ? paymentRequestDescription
              : `${job.category.label} - ${job.postcode}`,
            reference: '',
            dueDate,
            jobId: job.id,
            quoteId,
            companyId: String(companyId),
            consumerId: job.consumer.id,
            firstName: job.consumer.firstName || '',
            lastName: job.consumer.lastName || '',
            emailAddress: job.consumer.email || '',
            returnUrl: 'https://checkatrade.com',
            splits,
          },
          channelId: job.channelId,
          companyId: Number(companyId),
        };

        await createPaymentRequestMutation.mutateAsync(payload);

        showToast({
          type: 'success',
          text1: 'Successfully created payment request',
        });

        navigation.dispatch(StackActions.popToTop());
        navigation.navigate(QUOTES_SCREEN);
      } else if (
        quote?.homeownerDetails.name &&
        quote?.homeownerDetails.postcode &&
        consumerPhone &&
        consumerEmail &&
        jobCategory
      ) {
        //TODO change the quote to have separate first and last name fields
        const payload = {
          paymentRequest: {
            quoteId,
            amount: amountObject,
            description: paymentRequestDescription,
            dueDate,
            companyId: String(companyId),
            firstName: consumerFirstName,
            lastName: consumerLastName,
            emailAddress: consumerEmail,
            splits,
            reference: '',
          },
          job: {
            categoryId: jobCategory.categoryId.toString(),
            description: `${jobCategory.name} in ${quote.homeownerDetails.postcode}`,
            postcode: quote.homeownerDetails.postcode,
          },
          consumer: {
            firstName: consumerFirstName,
            lastName: consumerLastName,
            emailAddress: consumerEmail,
            phoneNumber: consumerPhone,
          },
        } as PaymentRequestOffPlatformJobRequest;

        await createOffPlatformPaymentRequestMutation.mutateAsync(payload);

        showToast({
          type: 'success',
          text1: 'Successfully created payment request',
        });

        navigation.dispatch(StackActions.popToTop());
        navigation.navigate(QUOTES_SCREEN);
      } else {
        const error = new Error(
          'Missing required fields to create a payment request',
        );

        captureException(error, {
          tags: {
            module: 'JobPaymentsQuoteNewPayment',
            method: 'handleSubmit',
          },
        });
        throw error;
      }
    } catch (error) {
      showToast({
        type: 'error',
        text1: 'Could not create payment request',
      });
    }
  };

  // Effects
  useEffect(() => {
    if (quote?.homeownerDetails.emailAddress) {
      setConsumerEmail(quote?.homeownerDetails.emailAddress);
    }

    if (quote?.homeownerDetails.phoneNumber) {
      setConsumerPhone(quote?.homeownerDetails.phoneNumber);
    }
  }, [quote?.homeownerDetails]);

  return (
    <View style={styles.flex} testID={TEST_IDS.ROOT}>
      <ScrollView
        style={styles.screen}
        contentContainerStyle={[
          styles.contentContainer,
          { paddingBottom: bottom },
        ]}
      >
        <View style={styles.contentContainer}>
          <View style={styles.flexRow}>
            <Typography
              testID={TEST_IDS.AMOUNT_TITLE}
              useVariant="bodySMSemiBold"
            >
              {QUOTE_PAYMENT_REQUEST_LABELS.SCREEN_TITLE}
            </Typography>
            <Typography
              testID={TEST_IDS.AMOUNT_VALUE}
              useVariant="bodySMSemiBold"
            >
              {formatCurrencyInput(amount)}
            </Typography>
          </View>
          <View style={styles.flexRow}>
            {percentageTypeSelected ? (
              <InputField
                style={styles.input}
                testID={TEST_IDS.PERCENTAGE_INPUT}
                keyboardType="decimal-pad"
                label={QUOTE_PAYMENT_REQUEST_LABELS.PERCENTAGE}
                onChangeText={updatePercentage}
                placeholder={QUOTE_PAYMENT_REQUEST_LABELS.PERCENTAGE}
                value={percentage}
                hideFocusBorder
                hintMessage={
                  percentageWarning ? undefined : formatCurrencyInput(amount)
                }
                error={percentageWarning}
              />
            ) : (
              <InputField
                style={styles.input}
                testID={TEST_IDS.AMOUNT_INPUT}
                keyboardType="decimal-pad"
                label={QUOTE_PAYMENT_REQUEST_LABELS.AMOUNT}
                onChangeText={setAmount}
                filter={currencyFilter}
                placeholder={QUOTE_PAYMENT_REQUEST_LABELS.AMOUNT}
                value={formatCurrencyInput(amount)}
                hideFocusBorder
                error={amountWarning}
              />
            )}
            <View style={styles.inputSelectionButtons}>
              <Button
                testID={TEST_IDS.CURRENCY_SYMBOL_BUTTON}
                onPress={toggleInputSelection}
                label={QUOTE_PAYMENT_REQUEST_LABELS.POUND_SYMBOL}
                variant="secondary"
                style={percentageTypeSelected && styles.backgroundColor}
                textStyle={percentageTypeSelected && styles.colorBlack}
              />
              <Button
                testID={TEST_IDS.PERCENTAGE_SYMBOL_BUTTON}
                onPress={toggleInputSelection}
                label={QUOTE_PAYMENT_REQUEST_LABELS.PERCENTAGE_SYMBOL}
                variant="secondary"
                style={!percentageTypeSelected && styles.backgroundColor}
                textStyle={!percentageTypeSelected && styles.colorBlack}
              />
            </View>
          </View>

          {!job && (
            <>
              <JobPaymentsOffPlatformCategoryDropdown
                onChange={setJobCategory}
                value={jobCategory}
              />
              <InputField
                testID={TEST_IDS.CONSUMER_FIRST_NAME_INPUT}
                label={QUOTE_PAYMENT_REQUEST_LABELS.CONSUMER_FIRST_NAME}
                onChangeText={setConsumerFirstName}
                placeholder={QUOTE_PAYMENT_REQUEST_LABELS.CONSUMER_FIRST_NAME}
                value={consumerFirstName}
                hideFocusBorder
                error={firstNameWarning}
              />
              <InputField
                testID={TEST_IDS.CONSUMER_LAST_NAME_INPUT}
                label={QUOTE_PAYMENT_REQUEST_LABELS.CONSUMER_LAST_NAME}
                onChangeText={setConsumerLastName}
                placeholder={QUOTE_PAYMENT_REQUEST_LABELS.CONSUMER_LAST_NAME}
                value={consumerLastName}
                hideFocusBorder
                error={lastNameWarning}
              />
              <InputField
                testID={TEST_IDS.CONSUMER_EMAIL_INPUT}
                label={QUOTE_PAYMENT_REQUEST_LABELS.CONSUMER_EMAIL}
                onChangeText={setConsumerEmail}
                placeholder={QUOTE_PAYMENT_REQUEST_LABELS.CONSUMER_EMAIL}
                value={consumerEmail}
                hideFocusBorder
                error={emailWarning}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
              <InputField
                testID={TEST_IDS.CONSUMER_PHONE_INPUT}
                label={QUOTE_PAYMENT_REQUEST_LABELS.CONSUMER_PHONE}
                onChangeText={setConsumerPhone}
                placeholder={QUOTE_PAYMENT_REQUEST_LABELS.CONSUMER_PHONE}
                value={consumerPhone}
                hideFocusBorder
                error={phoneWarning}
                keyboardType="phone-pad"
              />
            </>
          )}
          <InputField
            testID={TEST_IDS.DESCRIPTION_INPUT}
            label={QUOTE_PAYMENT_REQUEST_LABELS.DESCRIPTION}
            onChangeText={setPaymentRequestDescription}
            placeholder={QUOTE_PAYMENT_REQUEST_LABELS.DESCRIPTION}
            value={paymentRequestDescription}
            hideFocusBorder
          />
          <DatePicker
            testID={TEST_IDS.DUE_DATE_INPUT}
            label={QUOTE_PAYMENT_REQUEST_LABELS.DUE_DATE}
            value={dueDate || new Date()}
            onDateChanged={setDueDate}
            onDatePickerShown={noop}
            minimumDate={startOfToday()}
            maximumDate={getEndOfDayOfXDaysFromToday(
              NEW_PAYMENT_REQUEST_MAX_DAYS_DUE_DATE,
            )}
          />
          <JobPaymentsBreakdownCosts
            data={percentageWarning ? { amount: 0 } : curriedData}
            pending={waitingForDebounce || breakdownLoading}
          />
        </View>
        <Button
          testID={TEST_IDS.CREATE_BUTTON}
          onPress={handleSubmit}
          label={QUOTE_PAYMENT_REQUEST_LABELS.SUBMIT_BUTTON}
          variant="secondary"
          block
          isDisabled={!hasRequiredDetails}
        />
      </ScrollView>
    </View>
  );
}

JobPaymentsQuoteNewPayment.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  flex: { flex: 1 },
  screen: { flex: 1, padding: spacing(3) },
  contentContainer: { gap: spacing(2), justifyContent: 'space-between' },
  flexRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  input: {
    flex: 3,
  },
  inputSelectionButtons: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: palette.mortar.tokenColorLightBlue,
    maxHeight: 50,
    alignItems: 'center',
    borderRadius: 4,
    paddingHorizontal: spacing(1),
  },
  backgroundColor: { backgroundColor: palette.mortar.tokenColorLightBlue },
  colorBlack: { color: palette.mortarV3.tokenNeutral900 },
}));

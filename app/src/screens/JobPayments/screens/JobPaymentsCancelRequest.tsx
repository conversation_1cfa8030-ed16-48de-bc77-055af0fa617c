import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  KeyboardAvoidingView,
  LayoutChangeEvent,
  ScrollView,
  View,
} from 'react-native';
import { logEvent } from 'src/services/analytics';

import { useSafeAreaInsets } from 'react-native-safe-area-context';

import {
  createMortarStyles,
  createTestIds,
  isTruthy,
} from '@cat-home-experts/react-native-utilities';
import { Separator, showToast } from 'src/components';

import {
  Typography,
  RadioGroup,
  TextArea,
  Button,
  LoadingGuard,
} from '@cat-home-experts/react-native-components';

import { RouteProp, useRoute } from '@react-navigation/native';
import {
  BOTTOM_TABS_SCREEN,
  CHANNEL_SCREEN,
  JOB_PAYMENTS_CANCEL_REQUEST_SCREEN,
  JOB_PAYMENTS_SCREEN,
} from 'src/constants';
import { ANALYTICS_ACTION_TYPE, EVENT_TYPE } from 'src/constants.events';
import { useKeyboardHeight } from 'src/hooks/useKeyboardHeight';
import { useNavAwareReplace } from 'src/hooks/useNavAwareReplace';
import { isPlatform } from 'src/utilities/isPlatform';
import { useQueryClient } from '@tanstack/react-query';
import { GET_ACTIVITIES_KEY } from 'src/screens/JobPayments/hooks/useGetActivities';
import {
  MainNavigationType,
  useMainNavType,
} from 'src/navigation/MainNavigation/useMainNavType';
import { tradeAppBff } from 'src/data/api/trade-app-bff';
import { useUserContext } from 'src/hooks/useUser';
import type { AllScreensParamList } from 'src/navigation/routes';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import {
  MAXIMUM_OTHER_REASON_LENGTH,
  MINIMUM_OTHER_REASON_LENGTH,
  PAYMENT_REQUEST_CANCELLATION_LABELS,
  PAYMENT_REQUEST_CANCELLATION_REASONS,
} from '../constants';
import { useGetPaymentRequestDetail } from '../hooks/useGetPaymentRequestDetail';
import { PageNotFound } from '../../PageNotFound';

const TEST_IDS = createTestIds('job-payments-cancel-request', {
  RADIO_GROUP: 'radio-group',
  OTHER_REASON_TEXT_AREA: 'other-reason-text-area',
  SUBMIT_BUTTON: 'submit-button',
});

export function JobPaymentsCancelRequest(): ReturnType<React.FC> {
  // Refs
  const scrollRef = useRef<ScrollView>(null);
  const inputFocusedRef = useRef<boolean>(false);

  // State
  const [bottomSegmentHeight, setBottomSegmentHeight] = useState(0);
  const [reasonValue, setReasonValue] = useState<string>();
  const [otherReasonText, setOtherReasonText] = useState<string>('');
  const [reasonError, setReasonError] = useState<string>('');

  // Computed Values
  const client = useQueryClient();
  const keyboardHeight = useKeyboardHeight();
  const { bottom } = useSafeAreaInsets();
  const mainNavType = useMainNavType();
  const { companyId } = useUserContext();
  const navigationReplace = useNavAwareReplace();
  const route =
    useRoute<
      RouteProp<AllScreensParamList, typeof JOB_PAYMENTS_CANCEL_REQUEST_SCREEN>
    >();
  const { paymentLinkId, jobId, channelId } = route.params ?? {};
  if (!paymentLinkId) {
    throw new InvalidNavParamsError();
  }

  const {
    paymentRequestData,
    invalidatePaymentRequestDetail,
    isLoading,
    hasError,
  } = useGetPaymentRequestDetail(paymentLinkId);

  const validateOtherReason = useCallback((): boolean => {
    if (reasonValue !== PAYMENT_REQUEST_CANCELLATION_LABELS.OTHER_OPTION) {
      return true;
    }

    const doesOtherReasonMeetMinimal =
      otherReasonText.length >= MINIMUM_OTHER_REASON_LENGTH;

    if (doesOtherReasonMeetMinimal) {
      setReasonError('');
    } else {
      if (otherReasonText.length !== 0) {
        setReasonError(
          PAYMENT_REQUEST_CANCELLATION_LABELS.ERROR_MIN_CHAR_LABEL,
        );
      }
    }

    return doesOtherReasonMeetMinimal;
  }, [otherReasonText.length, reasonValue]);

  const handleUpdateOtherReason = useCallback(
    (input: string) => {
      setOtherReasonText(input);
      setReasonError('');

      const meetsMinimumRequiredText = validateOtherReason();

      if (!meetsMinimumRequiredText) {
        setReasonError(
          PAYMENT_REQUEST_CANCELLATION_LABELS.ERROR_MIN_CHAR_LABEL,
        );
      }
    },
    [validateOtherReason],
  );

  const isReasonValid = useMemo(() => {
    return (
      !isTruthy(reasonValue) ||
      (reasonValue === PAYMENT_REQUEST_CANCELLATION_LABELS.OTHER_OPTION &&
        !validateOtherReason())
    );
  }, [reasonValue, validateOtherReason]);

  // Effects
  useEffect(() => {
    if (!keyboardHeight || !isPlatform('ios')) {
      return;
    }

    if (inputFocusedRef.current) {
      scrollRef.current?.scrollToEnd();
    }
  }, [keyboardHeight]);

  if (!isLoading && hasError) {
    return <PageNotFound fallbackScreenToNavigate={JOB_PAYMENTS_SCREEN} />;
  }

  // Methods

  const handleUpdateReason = (reason: string) => {
    setReasonError('');
    setReasonValue(reason);

    logEvent(
      `${EVENT_TYPE.PAYMENTS_CANCEL_REASON}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
      {
        job_id_v2: jobId,
        payment_request_id: paymentRequestData?.id,
        reason: reason,
      },
    );
  };

  const handleSubmit = async () => {
    if (!companyId) {
      throw new Error('Company ID is not available');
    }

    const isValid = validateOtherReason();
    if (!isValid) {
      setReasonError(PAYMENT_REQUEST_CANCELLATION_LABELS.ERROR_MIN_CHAR_LABEL);
    }

    const reason: string =
      reasonValue !== PAYMENT_REQUEST_CANCELLATION_LABELS.OTHER_OPTION
        ? reasonValue!
        : otherReasonText!;

    try {
      await tradeAppBff.payments.cancelPaymentRequest({
        paymentLinkId,
        reason,
        companyId,
      });

      await client.invalidateQueries({
        queryKey: [GET_ACTIVITIES_KEY],
      });

      showToast({
        text1: PAYMENT_REQUEST_CANCELLATION_LABELS.SUCCESSFUL_CANCEL_TOAST,
      });

      logEvent(
        `${EVENT_TYPE.PAYMENTS_CANCEL}_${ANALYTICS_ACTION_TYPE.SUBMITTED}`,
        {
          job_id_v2: jobId,
          payment_request_id: paymentRequestData?.id,
          reason: reason,
          reason_detail: otherReasonText,
        },
      );

      await invalidatePaymentRequestDetail();

      if (channelId) {
        navigationReplace({
          destination: CHANNEL_SCREEN,
          params: { channelId: channelId },
          historyPredicate: (_, i, arr) =>
            i <
            arr.length - (mainNavType === MainNavigationType.DRAWER ? 2 : 3),
        });
      } else {
        navigationReplace({
          destination: JOB_PAYMENTS_SCREEN,
          historyPredicate: ({ key }) =>
            key.includes(BOTTOM_TABS_SCREEN) ||
            // Added hyphen to ensure it does not match payment request cancel screen
            key.includes(`${JOB_PAYMENTS_SCREEN}-`),
        });
      }
    } catch (error) {
      showToast({
        text1: PAYMENT_REQUEST_CANCELLATION_LABELS.UNSUCCESSFUL_CANCEL_TOAST,
        type: 'error',
      });
    }
  };

  const handleBottomSegmentLayout = (event: LayoutChangeEvent) =>
    setBottomSegmentHeight(event.nativeEvent.layout.height);

  return (
    <View style={styles.flexContainer}>
      <LoadingGuard style={styles.flexContainer} spinnerSize={40}>
        {!isLoading && !hasError && (
          <KeyboardAvoidingView
            behavior={isPlatform('ios') ? 'padding' : undefined}
            style={styles.flex}
            keyboardVerticalOffset={64}
          >
            <ScrollView
              ref={scrollRef}
              automaticallyAdjustContentInsets
              contentContainerStyle={[
                styles.scrollContent,
                // eslint-disable-next-line react-native/no-inline-styles
                { paddingBottom: keyboardHeight ? bottomSegmentHeight : 0 },
              ]}
            >
              <View style={styles.contentWrapper}>
                <Typography useVariant="bodyRegular">
                  {PAYMENT_REQUEST_CANCELLATION_LABELS.DESCRIPTION}
                </Typography>
                <View style={styles.reasonWrapper}>
                  <RadioGroup
                    accessibilityLabel={
                      PAYMENT_REQUEST_CANCELLATION_LABELS.REASONS_ACCESSIBILITY_LABEL
                    }
                    options={PAYMENT_REQUEST_CANCELLATION_REASONS}
                    value={reasonValue}
                    onChange={handleUpdateReason}
                    testID={TEST_IDS.RADIO_GROUP}
                  />

                  {reasonValue ===
                    PAYMENT_REQUEST_CANCELLATION_LABELS.OTHER_OPTION && (
                    <>
                      <Typography
                        useVariant="bodySmall"
                        style={styles.characterCount}
                      >
                        {`${MAXIMUM_OTHER_REASON_LENGTH - otherReasonText.length} characters remaining`}
                      </Typography>
                      <TextArea
                        onFocus={() => {
                          inputFocusedRef.current = true;
                        }}
                        onBlur={() => {
                          inputFocusedRef.current = false;
                        }}
                        label={
                          PAYMENT_REQUEST_CANCELLATION_LABELS.OTHER_REASON_LABEL
                        }
                        value={otherReasonText}
                        onChangeText={handleUpdateOtherReason}
                        keyboardType="default"
                        placeholder={
                          PAYMENT_REQUEST_CANCELLATION_LABELS.OTHER_REASON_PLACEHOLDER
                        }
                        hideLabel
                        multiline
                        error={reasonError}
                        showErrorBorder={Boolean(reasonError)}
                        maxLength={MAXIMUM_OTHER_REASON_LENGTH}
                        hideFocusBorder
                        containerStyle={styles.textAreaContainer}
                        inputStyle={styles.textAreaInput}
                        testID={TEST_IDS.OTHER_REASON_TEXT_AREA}
                      />
                    </>
                  )}
                </View>
              </View>
            </ScrollView>

            <View style={{ paddingBottom: bottom }}>
              <View style={styles.bg} onLayout={handleBottomSegmentLayout}>
                <Separator />
                <View style={styles.buttonWrapper}>
                  <Button
                    label={PAYMENT_REQUEST_CANCELLATION_LABELS.BUTTON_LABEL}
                    onPress={handleSubmit}
                    isDisabled={isReasonValid}
                    block
                    variant="secondary"
                    size="small"
                    testID={TEST_IDS.SUBMIT_BUTTON}
                  />
                </View>
              </View>
            </View>
          </KeyboardAvoidingView>
        )}
      </LoadingGuard>
    </View>
  );
}

JobPaymentsCancelRequest.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  bg: {
    backgroundColor: palette.mortarV3.tokenNeutral0,
  },
  flexContainer: {
    flex: 1,
    justifyContent: 'space-between',
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  flex: { flex: 1 },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: spacing(3),
  },
  contentWrapper: {
    paddingVertical: spacing(2),
    gap: spacing(1),
  },
  reasonWrapper: {
    paddingTop: spacing(2),
    gap: spacing(1),
  },
  characterCount: {
    fontSize: spacing(1.5),
    textAlign: 'right',
    color: palette.mortarV3.tokenNeutral600,
    marginTop: spacing(0.5),
  },
  textAreaContainer: {
    borderColor: palette.mortarV3.tokenNeutral200,
    minHeight: spacing(12),
    marginBottom: spacing(1),
  },
  textAreaInput: {
    minHeight: spacing(12),
    fontSize: spacing(2),
  },
  buttonWrapper: {
    paddingHorizontal: spacing(3),
    paddingVertical: spacing(2),
  },
}));

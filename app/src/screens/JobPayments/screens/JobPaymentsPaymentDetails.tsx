import React, { useMemo, useState } from 'react';
import { RouteProp, useRoute } from '@react-navigation/native';
import { ScrollView, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import {
  Button,
  LoadingGuard,
} from '@cat-home-experts/react-native-components';
import { TapToPay } from '@cat-home-experts/mortar-iconography-native';
import { useGetPaymentRequestDetail } from 'src/screens/JobPayments/hooks/useGetPaymentRequestDetail';
import type { AllScreensParamList } from 'src/navigation/routes';
import {
  JOB_PAYMENTS_PAYMENT_DETAILS_SCREEN,
  JOB_PAYMENTS_SCREEN,
} from 'src/constants';
import { JobPaymentsDetailView } from 'src/screens/JobPayments/screens/JobPaymentsDetailView';
import {
  JOB_PAYMENTS_DETAILS_STRINGS,
  JOB_PAYMENTS_SPINNER_SIZE,
  PAYMENTS_DASHBOARD_BOTTOM_SHEET_STRINGS,
  PAYMENTS_WEB_HOSTED_FAQS,
} from 'src/screens/JobPayments/constants';
import { MaxWidthContainer } from 'src/components/MaxWidthContainer';
import { PageNotFound } from 'src/screens/PageNotFound';
import { JobPaymentsDetailsScreenHeader } from 'src/screens/JobPayments/components/JobPaymentsDetailsScreenHeader';
import { PaymentsRequestBottomMenu } from 'src/screens/JobPayments/components/PaymentsRequestBottomSheet';
import { openURL } from 'src/utilities/linking';
import { logEvent } from 'src/services/analytics';
import { ANALYTICS_ACTION_TYPE, EVENT_TYPE } from 'src/constants.events';
import { PaymentRequestStatus } from 'src/data/schemas/api/capi/payments/payment-request';
import { useTapToPay } from 'src/hooks/useTapToPay';
import { showToast } from 'src/components';
import { useUserContext } from 'src/hooks/useUser';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';
import { useTapToPayWarmUp } from '../hooks/useTapToPayWarmUp';
import { useTempPaidPaymentRequestsAtom } from '../paymentState';

const TEST_IDS = createTestIds('job-payments-details', {
  TAP_TO_PAY_BUTTON: 'tap-to-pay-button',
});

export function JobPaymentsPaymentDetail(): ReturnType<React.FC> {
  // State
  const [tempPaidPaymentRequests, setTempPaidPaymentRequests] =
    useTempPaidPaymentRequestsAtom();
  const { makePayment, tapToPayAvailable } = useTapToPay();
  const { tapToPayWarmedUp, loading: tapToPayWarmUpLoading } =
    useTapToPayWarmUp();
  const [isPaymentsBottomSheetVisible, setIsPaymentsBottomSheetVisible] =
    useState(false);
  const { companyId } = useUserContext();

  // Route
  const route =
    useRoute<
      RouteProp<AllScreensParamList, typeof JOB_PAYMENTS_PAYMENT_DETAILS_SCREEN>
    >();
  const { paymentLinkId, channelId } = route.params ?? {};
  if (!paymentLinkId) {
    throw new InvalidNavParamsError();
  }

  // Hooks
  const { bottom } = useSafeAreaInsets();
  const { paymentRequestData, isLoading } =
    useGetPaymentRequestDetail(paymentLinkId);

  const transformed = useMemo(() => {
    if (
      !paymentRequestData ||
      !tempPaidPaymentRequests[paymentRequestData.id]
    ) {
      return paymentRequestData;
    }

    return { ...paymentRequestData, status: PaymentRequestStatus.Paid };
  }, [paymentRequestData, tempPaidPaymentRequests]);

  // Methods
  const handleTapToPay = async () => {
    if (!companyId) {
      return;
    }

    logEvent(
      `${EVENT_TYPE.PAYMENTS_REQUEST_TAP_TO_PAY}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
      {
        job_id_v2: paymentRequestData!.jobId,
        payment_status: paymentRequestData!.status,
      },
    );

    const result = await makePayment(paymentRequestData!);

    if (result?.Response?.Result === 'Success') {
      logEvent(EVENT_TYPE.PAYMENTS_REQUEST_TAP_TO_PAY_SUCCESS, {
        job_id_v2: paymentRequestData!.jobId,
        payment_status: paymentRequestData!.status,
      });

      setTempPaidPaymentRequests({ [paymentRequestData!.id]: Date.now() });

      showToast({
        type: 'success',
        text1: `You received £${result.PaymentResult.AmountsResp.AuthorizedAmount}`,
      });
    } else {
      logEvent(EVENT_TYPE.PAYMENTS_REQUEST_TAP_TO_PAY_FAILED, {
        job_id_v2: paymentRequestData!.jobId,
        payment_status: paymentRequestData!.status,
      });
      showToast({
        type: 'error',
        text1: 'Tap to Pay failed',
      });
    }
  };

  const handleDismissPaymentsBottomSheet = () => {
    setIsPaymentsBottomSheetVisible(false);
  };

  const handlePaymentsFaqs = async () => {
    handleDismissPaymentsBottomSheet();
    logEvent(
      `${EVENT_TYPE.PAYMENTS_REQUEST_OVERFLOW_FAQ}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
      {
        job_id_v2: paymentRequestData!.jobId,
        payment_status: paymentRequestData!.status,
      },
    );
    openURL(PAYMENTS_WEB_HOSTED_FAQS);
  };

  const handlePresentMoreMenu = () => {
    setIsPaymentsBottomSheetVisible(true);
    logEvent(
      `${EVENT_TYPE.PAYMENTS_REQUEST_OVERFLOW}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
      {
        job_id_v2: paymentRequestData!.jobId,
        payment_status: paymentRequestData!.status,
      },
    );
  };

  if (!paymentRequestData && !isLoading) {
    return <PageNotFound fallbackScreenToNavigate={JOB_PAYMENTS_SCREEN} />;
  }

  return (
    <View style={styles.flex}>
      <JobPaymentsDetailsScreenHeader
        title="Payment request"
        onPressMoreMenu={handlePresentMoreMenu}
      />
      <LoadingGuard
        style={[styles.flex, styles.container]}
        spinnerSize={JOB_PAYMENTS_SPINNER_SIZE}
      >
        {!isLoading && transformed && (
          <ScrollView
            testID={TEST_IDS.ROOT}
            contentContainerStyle={styles.contentContainer}
          >
            <MaxWidthContainer maxWidth={800}>
              <JobPaymentsDetailView
                paymentRequestData={transformed}
                channelId={channelId}
              />
            </MaxWidthContainer>
          </ScrollView>
        )}
        <PaymentsRequestBottomMenu
          isVisible={isPaymentsBottomSheetVisible}
          onOpeningPaymentFaqs={handlePaymentsFaqs}
          onClose={handleDismissPaymentsBottomSheet}
          paymentsFaqsLabel={
            PAYMENTS_DASHBOARD_BOTTOM_SHEET_STRINGS.PAYMENTS_FAQS_BUTTON_TEXT
          }
        />
        {(tapToPayWarmedUp || tapToPayWarmUpLoading) &&
          tapToPayAvailable &&
          transformed?.status === PaymentRequestStatus.PaymentPending && (
            <View
              style={[
                styles.stickyButtons,
                {
                  paddingBottom: Math.max(bottom, styles.stickyButtons.padding),
                },
              ]}
            >
              <Button
                testID={TEST_IDS.TAP_TO_PAY_BUTTON}
                iconStart={TapToPay}
                label={JOB_PAYMENTS_DETAILS_STRINGS.TAP_TO_PAY_BUTTON}
                variant="secondary"
                size="small"
                block
                onPress={handleTapToPay}
                isDisabled={tapToPayWarmUpLoading}
              />
            </View>
          )}
      </LoadingGuard>
    </View>
  );
}

JobPaymentsPaymentDetail.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  flex: { flex: 1 },
  container: {
    backgroundColor: palette.mortarV3.tokenDefault100,
  },
  stickyButtons: {
    padding: spacing(3),
    borderTopWidth: 1,
    borderColor: palette.mortar.tokenColorLighterGrey,
    backgroundColor: palette.mortarV3.tokenNeutral0,
  },
  contentContainer: {
    paddingBottom: spacing(3),
  },
}));

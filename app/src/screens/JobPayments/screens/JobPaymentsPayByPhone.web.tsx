import React, { useRef, useState, useCallback, useMemo } from 'react';
import { z } from 'zod';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';

import {
  AdyenCheckout,
  Card,
  CoreConfiguration,
  PaymentFailedData,
  SubmitActions,
  SubmitData,
} from '@adyen/adyen-web';
import '@adyen/adyen-web/styles/adyen.css';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { useNavigationHelpers } from 'src/hooks/useNavigationHelpers';
import {
  JOB_PAYMENTS_PAY_BY_PHONE_SCREEN,
  JOB_PAYMENTS_PAYMENT_DETAILS_SCREEN,
  JOB_PAYMENTS_SCREEN,
} from 'src/constants';
import { RouteProp, useRoute, useFocusEffect } from '@react-navigation/native';
import { AllScreensParamList } from 'src/navigation/routes';
import { config } from 'src/config';
import { useUserContext } from 'src/hooks/useUser';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { View } from 'react-native';
import { ContentSegment } from 'src/components/ContentSegment';
import { Typography } from '@cat-home-experts/react-native-components';
import {
  CurrencyType,
  PaymentPayByPhonePaymentRequest,
  SplitType,
} from 'src/data/schemas/api/capi/payments/payment-request';
import { InvalidNavParamsError } from 'src/components/ErrorBoundary';

import { useGetPayByPhonePaymentMethods } from '../hooks/useGetPayByPhonePaymentMethods';
import '../styles/AdyenPayByPhoneStylesOverride.css';
import { usePayByPhonePaymentRequest } from '../hooks/usePayByPhonePaymentRequest';
import { useGetCostBreakdown } from '../hooks/useGetCostBreakdown';
import { UseOffPlatformPaymentRequestForm } from '../hooks/useOffPlatformPaymentRequestForm';

const TEST_IDS = createTestIds('pay-by-phone', {
  CHECKOUT_CONTAINER: 'checkout-container',
  ERROR_MESSAGE: 'error-message',
  SUBMIT_BUTTON: 'submit-button',
});

export type JobPaymentsPayByPhoneScreenParams = Partial<
  z.infer<UseOffPlatformPaymentRequestForm['schema']>
> & {
  consumerId?: string;
  opportunityId?: string;
  jobId?: string;
  description?: string;
};

const currency = 'GBP';
const countryCode = 'GB';
const locale = 'en-US';
export function JobPaymentsPayByPhone(): ReturnType<React.FC> {
  const { replaceUntilScreen } = useNavigationHelpers();
  const isDesktop = useDesktopMediaQuery();
  const { mutateAsync: payByPhonePaymentRequest } =
    usePayByPhonePaymentRequest();
  const { companyId } = useUserContext();
  const { params } =
    useRoute<
      RouteProp<AllScreensParamList, typeof JOB_PAYMENTS_PAY_BY_PHONE_SCREEN>
    >();
  if (!params) {
    throw new InvalidNavParamsError();
  }

  const amount = useMemo(() => params.amount ?? 0, [params.amount]);
  const adyensDropinContainerRef = useRef<HTMLDivElement>(null);
  const cardRef = useRef<Card | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const isInitializingRef = useRef(false);
  const { data: breakdown } = useGetCostBreakdown(amount);

  const { data: payByPhonePaymentMethods } = useGetPayByPhonePaymentMethods();

  const onPaymentFailedHandler = useCallback(
    (error: PaymentFailedData) => {
      setErrorMessage(error.resultCode);
    },
    [setErrorMessage],
  );

  const onPaymentSuccessHandler = useCallback(
    (paymentLinkId: string) => {
      replaceUntilScreen(JOB_PAYMENTS_SCREEN, {
        screen: JOB_PAYMENTS_PAYMENT_DETAILS_SCREEN,
        params: { paymentLinkId },
      });
    },
    [replaceUntilScreen],
  );

  const onSubmitHandler = useCallback(
    async (state: SubmitData, _: unknown, actions: SubmitActions) => {
      try {
        const amountValue = Number(amount);
        const serviceChargeValue = Number(breakdown?.serviceCharge ?? 0);

        const paymentRequest = {
          amount: {
            currency: CurrencyType.GBP,
            value: amountValue,
          },
          jobId: params.jobId,
          opportunityId: params.opportunityId,
          consumerId: params.consumerId,
          description: params.description ?? '',
          reference: params.reference ?? '',
          companyId: companyId?.toString() ?? '',
          dueDate: params.dueDate ? new Date(params.dueDate) : new Date(),
          firstName:
            state.data.paymentMethod?.holderName?.split(' ')[0] ||
            params.firstName ||
            '',
          lastName:
            state.data.paymentMethod?.holderName
              ?.split(' ')
              .slice(1)
              .join(' ') ||
            params.lastName ||
            '',
          emailAddress:
            state.data.paymentMethod?.billingAddress?.email ||
            params.emailAddress ||
            '',
          phoneNumber: params.phoneNumber,
          splits: [
            {
              type: SplitType.Checkatrade,
              amount: {
                value: serviceChargeValue,
                currency: CurrencyType.GBP,
              },
            },
            {
              type: SplitType.Trader,
              amount: {
                value: amountValue,
                currency: CurrencyType.GBP,
              },
            },
          ],
        };

        const paymentCheckoutData = {
          ...state.data,
          firstName: params.firstName,
          lastName: params.lastName,
          emailAddress: params.emailAddress,
          phoneNumber: params.phoneNumber,
          reference: paymentRequest.reference,
          paymentMethod: {
            ...state.data.paymentMethod,
            type: 'scheme',
          },
          returnUrl: '',
          merchantAccount: '',
          amount: {
            currency: CurrencyType.GBP,
            value: amountValue,
          },
        };

        const payload = {
          paymentRequest,
          payByPhonePaymentMethods,
          vendor: 'adyen',
          vendorData: paymentCheckoutData,
          job: {
            categoryId: params.category?.categoryId.toString() ?? '',
            description: `${params.category?.name} - ${params.postcode}`,
            postcode: params.postcode ?? '',
          },
        };

        const result = await payByPhonePaymentRequest(
          payload as PaymentPayByPhonePaymentRequest,
        );

        const vendorData = result.data.vendorData;

        if (result.data.paymentLinkId) {
          if (
            vendorData.resultCode === 'Authorised' &&
            result.data.paymentLinkId
          ) {
            onPaymentSuccessHandler(result.data.paymentLinkId);
          }
        } else {
          actions.resolve(vendorData);
        }
      } catch (error) {
        const errorInfo =
          error instanceof Error ? error.message : 'Payment submission failed';
        setErrorMessage(errorInfo);
        actions.reject();
      }
    },
    [
      amount,
      breakdown?.serviceCharge,
      companyId,
      params,
      payByPhonePaymentRequest,
      onPaymentSuccessHandler,
      payByPhonePaymentMethods,
    ],
  );

  const initializeAdyen = useCallback(async () => {
    if (isInitializingRef.current) {
      return;
    }

    try {
      isInitializingRef.current = true;
      const adyenConfiguration: CoreConfiguration = {
        clientKey: config.payByPhoneConfig.adyenClientKey,
        environment: config.payByPhoneConfig.environment,
        amount: {
          value: amount,
          currency,
        },
        locale,
        countryCode,
        onSubmit: onSubmitHandler,
        onPaymentFailed: onPaymentFailedHandler,
      };

      const checkout = await AdyenCheckout(adyenConfiguration);

      if (adyensDropinContainerRef.current) {
        cardRef.current = new Card(checkout).mount(
          adyensDropinContainerRef.current,
        );
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      setErrorMessage(errorMsg);
    } finally {
      isInitializingRef.current = false;
    }
  }, [amount, onSubmitHandler, onPaymentFailedHandler]);

  useFocusEffect(
    useCallback(() => {
      if (cardRef.current?.remove) {
        cardRef.current.remove();
      }

      initializeAdyen();

      return () => {
        if (cardRef.current?.remove) {
          cardRef.current.remove();
        }
      };
    }, [initializeAdyen, cardRef]),
  );

  return (
    <KeyboardAwareScrollView
      testID={TEST_IDS.ROOT}
      bottomOffset={10}
      style={[styles.flex, styles.scrollView]}
      contentContainerStyle={styles.contentContainer}
    >
      <View
        style={[
          styles.fullWidth,
          isDesktop && [styles.desktopContainer, styles.paddingTop],
        ]}
      >
        {errorMessage ? (
          <ContentSegment>
            <Typography testID={TEST_IDS.ERROR_MESSAGE}>
              {errorMessage}
            </Typography>
          </ContentSegment>
        ) : (
          <ContentSegment style={styles.detailsContainer}>
            <div
              id="adyen-checkout"
              ref={adyensDropinContainerRef}
              style={styles.checkoutContainer}
              data-testid={TEST_IDS.CHECKOUT_CONTAINER}
            />
          </ContentSegment>
        )}
      </View>
    </KeyboardAwareScrollView>
  );
}

JobPaymentsPayByPhone.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  flex: { flex: 1 },
  scrollView: { backgroundColor: palette.mortar.tokenColorLightBlue },
  contentContainer: { alignItems: 'center' },
  desktopContainer: { maxWidth: 800 },
  paddingTop: { paddingTop: spacing(3) },
  errorMessageContainer: {
    padding: spacing(2),
    backgroundColor: palette.mortarV3.tokenAttention50,
    borderRadius: spacing(1),
    marginBottom: spacing(2),
  },
  container: {
    maxWidth: 800,
    minWidth: 500,
    alignSelf: 'center',
    paddingTop: spacing(2),
  },
  fullWidth: { width: '100%' },
  buttonContainer: {
    paddingVertical: spacing(2),
    backgroundColor: palette.mortarV3.tokenNeutral0,
    alignItems: 'center',
  },
  detailsContainer: {
    paddingVertical: spacing(1),
  },
  checkoutContainer: {
    paddingVertical: spacing(1),
    paddingTop: spacing(1),
  },
}));

JobPaymentsPayByPhone.testIds = TEST_IDS;

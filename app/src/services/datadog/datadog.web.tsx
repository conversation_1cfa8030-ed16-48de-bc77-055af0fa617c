import React, { ReactNode } from 'react';
import {
  type RumInitConfiguration,
  DefaultPrivacyLevel,
  datadogRum,
} from '@datadog/browser-rum';
import { datadogLogs } from '@datadog/browser-logs';
import { TrackingConsent } from '@datadog/browser-core';
import type { NavigationContainerRef } from '@react-navigation/native';
import { isAxiosError } from 'axios';
import { GIT_SHA } from 'src/services/dotenv';
import { isError } from 'src/utilities/errors/isError';
import { config } from 'src/config';
import type { AllScreensParamList } from 'src/navigation/routes';
import { shouldIgnoreError } from './datadogHelpers';

const getDatadogConfig = (): RumInitConfiguration => {
  const isProduction = config.environmentName === 'production';
  return {
    applicationId: config.datadogConfig.applicationId,
    clientToken: config.datadogConfig.clientToken,
    site: 'datadoghq.eu',
    service: 'trade-experience-web',
    version: GIT_SHA,
    env: config.environmentName,
    sessionSampleRate: isProduction ? 25 : 0,
    traceSampleRate: isProduction ? 25 : 0,
    sessionReplaySampleRate: 0, // disabled
    trackUserInteractions: false,
    trackResources: true,
    trackLongTasks: true,
    defaultPrivacyLevel: DefaultPrivacyLevel.MASK,
    allowedTracingUrls: [
      config.apiBaseUrl,
      config.campaignsBaseUrl,
      config.capiUrl,
      config.memberDataApiBaseUrl,
    ],
  };
};

function initializeDatadog() {
  const ddConfig = getDatadogConfig();
  datadogRum.init(ddConfig);
  datadogLogs.init({
    clientToken: ddConfig.clientToken,
    site: ddConfig.site,
    forwardErrorsToLogs: false,
    forwardReports: 'all',
    sessionSampleRate: ddConfig.sessionSampleRate,
    env: ddConfig.env,
    service: ddConfig.service,
    version: ddConfig.version,
  });
}

export const setUser = (
  userContext: {
    id: string;
  } & Record<string, unknown>,
): Promise<void> => {
  datadogRum.setUser(userContext);
  return Promise.resolve();
};

export const clearUser = (): Promise<void> => {
  datadogRum.clearUser();
  return Promise.resolve();
};

export const setTrackingConsent = (granted: boolean): Promise<void> => {
  datadogRum.setTrackingConsent(
    granted ? TrackingConsent.GRANTED : TrackingConsent.NOT_GRANTED,
  );
  return Promise.resolve();
};

export const startTrackingViews = (
  _navigationRef: NavigationContainerRef<AllScreensParamList>,
): void => {
  // Not supported on web
};

export const stopTrackingViews = (
  _navigationRef: NavigationContainerRef<AllScreensParamList>,
): void => {
  // Not supported on web
};

export const captureException = (
  ex: unknown | Error,
  context?: Record<string, unknown>,
): Promise<void> => {
  if (__DEV__) {
    console.error(ex, context);
  }

  if (!isError(ex) || shouldIgnoreError(ex)) {
    // Network error, timeout or explicitly ignored
    return Promise.resolve();
  }

  const axiosContext = isAxiosError(ex)
    ? {
        statusCode: ex.response?.status,
        statusText: ex.response?.statusText,
      }
    : undefined;

  datadogRum.addError(ex, {
    axiosContext,
    ...context,
  });

  return Promise.resolve();
};

export const captureLog = (
  level: 'debug' | 'info' | 'warn' | 'error',
  message: string,
  context?: Record<string, unknown>,
): Promise<void> => {
  return Promise.resolve(datadogLogs.logger[level](message, context));
};

export const addAction = (
  message: string,
  data?: Record<string, unknown>,
): Promise<void> => {
  datadogRum.addAction(message, data);
  return Promise.resolve();
};

export const crashJavascriptThread = (): void => {
  throw new Error('TEST: Crashing Javascript Thread');
};

/**
 * This provider is used to initialize the datadogRum instance
 * This also matches the react-native sdk implementation
 */
let isInitialized = false;
export const DatadogProvider = ({
  children,
}: React.PropsWithChildren): ReactNode => {
  if (!isInitialized) {
    initializeDatadog();
    isInitialized = true;
  }

  return <>{children}</>;
};

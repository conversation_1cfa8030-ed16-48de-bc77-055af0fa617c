import React, { memo, useCallback, useMemo } from 'react';
import { Typography } from '@cat-home-experts/react-native-components';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { essentialsTermsRoutes } from 'src/screens/EssentialsTerms/essentialsTermsRoutes';
import { ErrorBoundary } from 'src/components/ErrorBoundary';
import type { AllScreensParamList } from './routes';
import type { RouteItem } from './types/routeTypes';

const Stack = createNativeStackNavigator<AllScreensParamList>();

export const EssentialsTermsStack = memo((): ReturnType<React.FC> => {
  const renderScreen = useCallback(
    (route: RouteItem) => <Stack.Screen {...route} key={route.key} />,
    [],
  );
  const renderedRoutes = useMemo(
    () => essentialsTermsRoutes.map(renderScreen),
    [renderScreen],
  );
  return (
    <Stack.Navigator
      screenLayout={({ children, route, navigation }) => (
        <ErrorBoundary
          captureContext={{
            navStack: 'EssentialsTerms',
            route,
          }}
          goBack={navigation.goBack}
        >
          {children}
        </ErrorBoundary>
      )}
      screenOptions={() => ({
        headerBackVisible: false,
        headerShown: false,
        headerTitle: ({ children }) => (
          <Typography useVariant="subHeadingSemiBold">{children}</Typography>
        ),
      })}
    >
      {renderedRoutes}
    </Stack.Navigator>
  );
});

import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import type { NativeStackNavigationOptions } from '@react-navigation/native-stack';
import React, { memo, useCallback, useMemo } from 'react';
import { HOME_SCREEN } from 'src/constants';
import { useNavigationLinks } from 'src/context/NavigationLinksContext';
import { useBadges } from 'src/navigation/NavigationBadges';
import { ErrorBoundary } from 'src/components/ErrorBoundary';
import type { RouteItem } from '../types/routeTypes';
import { BottomTabBar } from './BottomTabBar';

const BottomTabs = createBottomTabNavigator();

export const BottomTabNavigation = memo((): ReturnType<React.FC> => {
  const { mainRoutes } = useNavigationLinks();
  const badges = useBadges();

  const renderScreen = useCallback(
    (route: RouteItem) => (
      <BottomTabs.Screen
        key={route.key}
        name={route.name}
        component={route.component}
        options={{
          tabBarBadge: badges[route.name],
          tabBarLabel:
            (route.options as NativeStackNavigationOptions)?.title ||
            route.name,
          tabBarAccessibilityLabel: `${route.name} tab`,
        }}
      />
    ),
    [badges],
  );

  const renderedRoutes = useMemo(
    () => mainRoutes.map(renderScreen),
    [mainRoutes, renderScreen],
  );

  return (
    <BottomTabs.Navigator
      initialRouteName={HOME_SCREEN}
      screenLayout={({ children, route, navigation }) => (
        <ErrorBoundary
          captureContext={{
            navStack: 'BottomTabNavigation',
            route,
          }}
          goBack={navigation.goBack}
        >
          {children}
        </ErrorBoundary>
      )}
      screenOptions={{
        tabBarHideOnKeyboard: true,
        headerShown: false,
      }}
      tabBar={(props) => <BottomTabBar {...props} />}
      backBehavior="history"
    >
      {renderedRoutes}
    </BottomTabs.Navigator>
  );
});

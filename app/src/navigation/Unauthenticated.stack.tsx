import React, { memo, useCallback, useMemo } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Typography } from '@cat-home-experts/react-native-components';
import { NavBackIcon } from 'src/components';
import { ErrorBoundary } from 'src/components/ErrorBoundary';
import { unauthenticatedRoutes } from './routes/unauthenticated';
import type { RouteItem } from './types/routeTypes';
import type { AllScreensParamList } from './routes';

const Stack = createNativeStackNavigator<AllScreensParamList>();

export const UnauthenticatedStack = memo((): ReturnType<React.FC> => {
  const renderScreen = useCallback(
    (route: RouteItem) => <Stack.Screen {...route} key={route.key} />,
    [],
  );
  const renderedRoutes = useMemo(
    () => unauthenticatedRoutes.map(renderScreen),
    [renderScreen],
  );
  return (
    <Stack.Navigator
      screenLayout={({ children, route, navigation }) => (
        <ErrorBoundary
          captureContext={{
            navStack: 'Unauthenticated',
            route,
          }}
          goBack={navigation.goBack}
        >
          {children}
        </ErrorBoundary>
      )}
      screenOptions={({ navigation }) => ({
        headerBackVisible: false,
        headerTitleAlign: 'center',
        headerTitle: ({ children }) => (
          <Typography useVariant="subHeadingSemiBold">{children}</Typography>
        ),
        headerLeft: () => <NavBackIcon onPress={navigation.goBack} />,
      })}
    >
      {renderedRoutes}
    </Stack.Navigator>
  );
});

import React, { memo, useCallback, useMemo } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useNavigationLinks } from 'src/context/NavigationLinksContext';
import { NavBackIcon } from 'src/components/primitives/NavBackIcon';
import { BOTTOM_TABS_SCREEN } from 'src/constants';
import { ErrorBoundary } from 'src/components/ErrorBoundary';
import { BottomTabNavigation } from './BottomTabNavigation';
import { MobileNavigationHeaderTitle } from './components/MobileNavigationHeaderTitle';
import type { RouteItem } from './types/routeTypes';
import type { AllScreensParamList } from './routes';

type MobileStackParamList = {
  BottomTabNavigation: undefined;
} & AllScreensParamList;

const Stack = createNativeStackNavigator<MobileStackParamList>();

export const MobileNavigation = memo((): ReturnType<React.FC> => {
  const { globalRoutes } = useNavigationLinks();
  const renderScreen = useCallback(
    ({ key, ...route }: RouteItem) => <Stack.Screen {...route} key={key} />,
    [],
  );

  const renderedRoutes = useMemo(
    () => globalRoutes.map(renderScreen),
    [globalRoutes, renderScreen],
  );

  // Add a guard to ensure routes are available
  if (!globalRoutes.length) {
    return null;
  }

  return (
    <Stack.Navigator
      initialRouteName={BOTTOM_TABS_SCREEN}
      screenLayout={({ children, route, navigation }) => (
        <ErrorBoundary
          captureContext={{
            navStack: 'MobileNavigation',
            route,
          }}
          goBack={navigation.goBack}
        >
          {children}
        </ErrorBoundary>
      )}
      screenOptions={({ navigation }) => ({
        headerBackVisible: false,
        headerTitleAlign: 'center',
        headerTitle: (props) => <MobileNavigationHeaderTitle {...props} />,
        headerLeft: () => <NavBackIcon onPress={navigation.goBack} />,
      })}
    >
      <Stack.Screen
        name={BOTTOM_TABS_SCREEN}
        component={BottomTabNavigation as React.ComponentType}
        options={{ headerShown: false }}
      />
      {renderedRoutes}
    </Stack.Navigator>
  );
});

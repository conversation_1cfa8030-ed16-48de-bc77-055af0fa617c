import { BOTTOM_TABS_SCREEN } from 'src/constants';
import type { GlobalScreensParamList } from './global';
import type { UnauthenticatedScreensParamList } from './unauthenticated';
import type { EssentialsTermsScreensParamList } from '../../screens/EssentialsTerms/essentialsTermsRoutes';

export { generateGlobalRoutes } from './global';

type MakeValuesUndefined<T> = {
  [P in keyof T]: T[P] | undefined;
};

type NavigationOnlyScreens = {
  [BOTTOM_TABS_SCREEN]: undefined;
};

type _AllScreensParamList = GlobalScreensParamList &
  NavigationOnlyScreens &
  UnauthenticatedScreensParamList &
  EssentialsTermsScreensParamList;

export type AllScreensParamList = MakeValuesUndefined<_AllScreensParamList>;

import React, { ReactElement } from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button, Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import ErrorImage from '../../assets/images/errors/man-with-broken-computer.svg';
import { InvalidNavParamsError } from './constants';

const TEST_IDS = createTestIds('error-boundary', {
  RELOAD_BUTTON: 'reload-button',
});

interface GenericErrorProps {
  style?: StyleProp<ViewStyle>;
  title: string;
  description: string;
  buttonLabel: string;
  onPress?: () => void;
}
const GenericError: React.FC<GenericErrorProps> = ({
  title,
  description,
  buttonLabel,
  onPress,
  style,
}) => (
  <SafeAreaView testID={TEST_IDS.ROOT} style={[styles.container, style]}>
    <ErrorImage />
    <Typography use="header">{title}</Typography>
    <Typography use="bodyRegular">{description}</Typography>
    {onPress ? (
      <Button
        testID={TEST_IDS.RELOAD_BUTTON}
        label={buttonLabel}
        variant="secondary"
        onPress={onPress}
        size="small"
        style={styles.button}
      />
    ) : null}
  </SafeAreaView>
);

export type FallbackComponentProps = {
  error: Error;
  goBack?: () => void;
  resetError: () => void;
};

export const FallbackComponent = ({
  error,
  goBack,
  resetError,
}: FallbackComponentProps): ReactElement => {
  if (error instanceof InvalidNavParamsError) {
    return (
      <GenericError
        title="We are unable find this page"
        description="Please go back and try again"
        buttonLabel="Go back"
        onPress={goBack}
      />
    );
  }

  return (
    <GenericError
      title="Something has gone wrong"
      description="Please refresh and try again"
      buttonLabel="Refresh"
      onPress={resetError}
    />
  );
};

FallbackComponent.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  button: {
    marginTop: spacing(4),
  },
}));

import React, { ReactElement, Component, ComponentType } from 'react';
import { captureException, captureLog } from 'src/services/datadog';
import {
  FallbackComponent as DefaultFallbackComponent,
  type FallbackComponentProps,
} from './FallbackComponent';
import { InvalidNavParamsError } from './constants';

type Context = Record<string, unknown>;
export interface ErrorBoundaryProps {
  children: ReactElement[] | ReactElement;
  FallbackComponent: ComponentType<FallbackComponentProps>;
  captureContext?: Context;
  goBack?: () => void;
}

interface ErrorBoundaryState {
  error: Error | null;
}

export class ErrorBoundary extends Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  static defaultProps: {
    FallbackComponent: ComponentType<FallbackComponentProps>;
  } = {
    FallbackComponent: DefaultFallbackComponent,
  };

  state = { error: null };

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { error };
  }

  componentDidCatch(error: Error): void {
    if (error instanceof InvalidNavParamsError) {
      captureLog('warn', 'Invalid navigation parameters', {
        ...this.props.captureContext,
        module: 'ErrorBoundary',
      });
    } else {
      captureException(error, {
        ...this.props.captureContext,
        module: 'ErrorBoundary',
        level: 'error',
      });
    }
  }

  resetError: () => void = () => {
    this.setState({ error: null });
  };

  render(): ReactElement | ReactElement[] {
    const { FallbackComponent, goBack } = this.props;
    const { error } = this.state;
    return error ? (
      <FallbackComponent
        error={error}
        resetError={this.resetError}
        goBack={goBack}
      />
    ) : (
      this.props.children
    );
  }
}
